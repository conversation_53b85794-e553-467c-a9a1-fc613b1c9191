import React, { useState } from 'react';
import { XMarkIcon, ChartBarIcon, ClockIcon, ArrowTrendingUpIcon, BoltIcon } from '@heroicons/react/24/outline';
import { Student } from '../types/student';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Legend, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { createPortal } from 'react-dom';

interface StudentAnalyticsModalProps {
    student: Student;
    onClose: () => void;
    currentClass: any;
}

const StudentAnalyticsModal: React.FC<StudentAnalyticsModalProps> = ({ student, onClose, currentClass }) => {
    const [activeTab, setActiveTab] = useState<'overview' | 'progress' | 'skills'>('overview');

    // Custom formatter for tooltips
    const percentFormatter = (value: any) => `${value.toFixed(1)}`;
    
    // Custom formatter for minutes
    const minutesFormatter = (value: any) => `${value} min`;
    
    // Custom formatter for questions
    const questionsFormatter = (value: any) => `${value} questions`;
    
    // Get the last active time for a student (most recent daily activity)
    const getLastActiveTime = (student: Student): string => {
        // Use the current class's subject
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return 'No data';
        
        const dailyActivities = subjectData.analytics?.progressTracking?.dailyActivity || [];
        if (dailyActivities.length === 0) return 'No activity';
        
        // Sort by date descending and get the most recent
        const sortedActivities = [...dailyActivities].sort((a, b) => 
            new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        
        if (sortedActivities.length === 0) return 'No recent activity';
        
        const mostRecent = new Date(sortedActivities[0].date);
        const now = new Date();
        const diffMs = now.getTime() - mostRecent.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            if (diffHours === 0) {
                const diffMinutes = Math.floor(diffMs / (1000 * 60));
                return `${diffMinutes} min ago`;
            }
            return `${diffHours} hr ago`;
        }
        
        return `${diffDays} days ago`;
    };
    
    // Get strengths for a student with unique values
    const getStrengths = (student: Student): string[] => {
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];
        
        const strengths = subjectData.analytics?.knowledgeAnalysis?.strengths || [];
        const mappedStrengths = strengths.map(s => s.subtopicId);
        // Return a unique array using Set
        return Array.from(new Set(mappedStrengths));
    };
    
    // Get weaknesses for a student with unique values
    const getWeaknesses = (student: Student): string[] => {
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];
        
        const weaknesses = subjectData.analytics?.knowledgeAnalysis?.weaknesses || [];
        const mappedWeaknesses = weaknesses.map(w => w.subtopicId);
        // Return a unique array using Set
        return Array.from(new Set(mappedWeaknesses));
    };
    
    // Get recent topics for a student
    const getRecentTopics = (student: Student): string[] => {
        // Use the current class's subject
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];
        
        // Get topics from recent tests
        const recentTests = subjectData.testHistory || [];
        if (recentTests.length === 0) return [];
        
        const topics = recentTests.slice(-3).map((test: any) => test.topics);
        return topics;
    };

    // Get progress data for a student
    const getProgressData = (student: Student) => {
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];
        
        const dailyActivities = subjectData.analytics?.progressTracking?.dailyActivity || [];
        if (dailyActivities.length === 0) return [];
        
        // Sort by date ascending
        const sortedActivities = [...dailyActivities].sort((a, b) => 
            new Date(a.date).getTime() - new Date(b.date).getTime()
        );
        
        // Return the last 7 activities or all if less than 7
        return sortedActivities.slice(-7).map(activity => ({
            date: new Date(activity.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            minutesSpent: activity.minutesSpent,
            questionsAnswered: activity.questionsAnswered,
            consistencyScore: activity.consistencyScore * 100
        }));
    };
    
    // Get skill radar data for a student
    const getSkillRadarData = (student: Student) => {
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];
        
        const strengths = subjectData.analytics?.knowledgeAnalysis?.strengths || [];
        const weaknesses = subjectData.analytics?.knowledgeAnalysis?.weaknesses || [];
        
        // Combine strengths and weaknesses to create a complete skill profile
        const allSkills = new Map();
        
        // Add strengths
        strengths.forEach(s => {
            allSkills.set(s.subtopicId, {
                subject: s.subtopicId,
                proficiency: s.proficiency * 100,
                confidence: s.confidenceScore * 100
            });
        });
        
        // Add weaknesses (if a skill is both in strengths and weaknesses, strengths will be overwritten)
        weaknesses.forEach(w => {
            allSkills.set(w.subtopicId, {
                subject: w.subtopicId,
                proficiency: w.proficiency * 100,
                confidence: 0 // Assuming no confidence score for weaknesses
            });
        });
        
        // Convert map to array
        return Array.from(allSkills.values());
    };

    const getTestHistoryTopics = (testId: string): string[] => {
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];

        const testHistory = subjectData.testHistory.find((test: any) => test._id === testId);
        if (!testHistory) return [];
        
        // Use type assertion to safely access topics
        const testWithTopics = testHistory as any;
        return Array.isArray(testWithTopics.topics) 
            ? testWithTopics.topics 
            : (testWithTopics.topics ? [testWithTopics.topics] : []);
    };
    
    // Get test performance data for a student
    const getTestPerformanceData = (student: Student) => {
        const currentSubject = currentClass?.subject?.toLowerCase() || '';
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
        if (!subjectData) return [];
        
        const tests = subjectData.attemptedTests || [];
        if (tests.length === 0) return [];
        
        // Return the last 5 tests or all if less than 5
        return tests.slice(-5).map((test, index) => {
            // Get topic names if available - using any to access potential topics property
            const topicNames = getTestHistoryTopics(test.testId) || [`Test ${index + 1}`];
            // console.error(test);
            // console.error(topicNames);
            const topicLabel = Array.isArray(topicNames) ? topicNames[0] : topicNames;
            
            // Calculate score as a percentage based on responses length
            const totalQuestions = test.responses?.length || 1;
            const correctAnswers = test.totalScore || 0;
            const scorePercentage = (correctAnswers / totalQuestions) * 100;
            
            return {
                name: topicLabel, // Use the first topic as the name
                score: scorePercentage,
                proficiencyBefore: (test.proficiencyBefore || 0) > 1 ? 
                    (test.proficiencyBefore || 0) : (test.proficiencyBefore || 0) * 100,
                proficiencyAfter: (test.proficiencyAfter || 0) > 1 ? 
                    (test.proficiencyAfter || 0) : (test.proficiencyAfter || 0) * 100,
                totalQuestions: totalQuestions,
                correctAnswers: correctAnswers,
            };
        });
    };
    
    // Custom tooltip for test performance chart
    const TestPerformanceTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-card p-3 border border-border shadow-md rounded-md">
                    <p className="font-medium text-sm">{label}</p>
                    {payload.map((entry: any, index: number) => (
                        <p key={index} style={{ color: entry.color }} className="text-xs">
                            {entry.name}: {percentFormatter(entry.value)} % 
                        </p>
                    ))}
                    {payload[0]?.payload?.topics && payload[0].payload.topics !== label && (
                        <p className="text-xs text-gray-600 mt-1">
                            <span className="font-medium">Topics:</span> {payload[0].payload.topics}
                        </p>
                    )}
                    {payload[0]?.payload?.correctAnswers !== undefined && (
                        <p className="text-xs text-gray-600">
                            <span className="font-medium">Score:</span> {payload[0].payload.correctAnswers} / {payload[0].payload.totalQuestions}
                        </p>
                    )}
                </div>
            );
        }
        return null;
    };
    
    // Custom tooltip for activity chart
    const ActivityTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-card p-3 border border-border shadow-md rounded-md">
                    <p className="font-medium text-sm">{label}</p>
                    {payload.map((entry: any, index: number) => {
                        let valueDisplay = entry.value;
                        
                        // Format based on data type
                        if (entry.dataKey === 'minutesSpent') {
                            valueDisplay = `${percentFormatter(entry.value)}`;
                        } else if (entry.dataKey === 'questionsAnswered') {
                            valueDisplay = `${percentFormatter(entry.value)}`;
                        } else if (entry.dataKey === 'consistencyScore') {
                            valueDisplay = percentFormatter(entry.value);
                        }
                        
                        return (
                            <p key={index} style={{ color: entry.color }} className="text-xs">
                                {entry.name}: {valueDisplay}
                            </p>
                        );
                    })}
                </div>
            );
        }
        return null;
    };

    // Custom formatter for XAxis tick labels
    const topicFormatter = (topicId: string) => {
        if (topicId.length > 10) {
            return `${topicId.substring(0, 10)}...`;
        }
        return topicId;
    };

    return createPortal(
        <div className="fixed inset-0 backdrop-blur-sm bg-opacity-30 flex items-center justify-center p-4 z-50">
            <div className="bg-card border border-border rounded-lg shadow-lg w-full max-w-4xl mx-auto" onClick={(e) => e.stopPropagation()}>
                {/* Modal Header */}
                <div className="px-6 py-4 border-b border-border flex justify-between items-center">
                    <div>
                        <h3 className="text-xl font-semibold text-primary">{student.firstName} {student.lastName}</h3>
                        {/* <p className="text-sm text-muted-foreground">@{student.username}</p> */}
                    </div>
                    <button onClick={onClose} className="text-muted-foreground hover:text-accent">
                        <XMarkIcon className="w-5 h-5" />
                    </button>
                </div>

                {/* Modal Tabs */}
                <div className="px-6 pt-4 flex gap-2 border-b border-border pb-2">
                    <button
                        onClick={() => setActiveTab('overview')}
                        className={`px-3 py-1 text-sm rounded-full transition-colors ${
                            activeTab === 'overview'
                            ? 'bg-accent text-white font-medium'
                            : 'bg-card text-muted-foreground hover:bg-muted border border-border'
                        }`}
                    >
                        Overview
                    </button>
                    <button
                        onClick={() => setActiveTab('progress')}
                        className={`px-3 py-1 text-sm rounded-full transition-colors ${
                            activeTab === 'progress'
                            ? 'bg-accent text-white font-medium'
                            : 'bg-card text-muted-foreground hover:bg-muted border border-border'
                        }`}
                    >
                        Progress
                    </button>
                    <button
                        onClick={() => setActiveTab('skills')}
                        className={`px-3 py-1 text-sm rounded-full transition-colors ${
                            activeTab === 'skills'
                            ? 'bg-accent text-white font-medium'
                            : 'bg-card text-muted-foreground hover:bg-muted border border-border'
                        }`}
                    >
                        Skills Analysis
                    </button>
                </div>

                {/* Modal Content */}
                <div className="p-6 max-h-[70vh] overflow-y-auto">
                    {activeTab === 'overview' && (
                        <>
                            {/* Overview Section */}
                            <div className="grid grid-cols-3 gap-4 mb-6">
                                <div className="p-4 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2 mb-2">
                                        <ArrowTrendingUpIcon className="w-4 h-4 text-accent" />
                                        <span className="text-sm text-muted-foreground">Proficiency</span>
                                    </div>
                                    <span className="text-2xl font-semibold text-primary">
                                        {percentFormatter(student.proficiency > 1 ?
                                            student.proficiency : student.proficiency * 100)} %
                                    </span>
                                </div>
                                <div className="p-4 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2 mb-2">
                                        <ClockIcon className="w-4 h-4 text-accent" />
                                        <span className="text-sm text-muted-foreground">Last Active</span>
                                    </div>
                                    <span className="text-2xl font-semibold text-primary">{getLastActiveTime(student)}</span>
                                </div>
                                <div className="p-4 bg-muted rounded-lg">
                                    <div className="flex items-center gap-2 mb-2">
                                        <ChartBarIcon className="w-4 h-4 text-accent" />
                                        <span className="text-sm text-muted-foreground">Tests Taken</span>
                                    </div>
                                    <span className="text-2xl font-semibold text-primary">
                                        {student.subjects.find(s => 
                                            s.subjectName.toLowerCase() === (currentClass?.subject?.toLowerCase() || '')
                                        )?.analytics?.performanceMetrics?.totalTestsTaken || 0}
                                    </span>
                                </div>
                            </div>

                            {/* Details Sections */}
                            <div className="space-y-6">
                                <div>
                                    <h4 className="text-sm font-medium text-muted-foreground uppercase mb-2">Recent Topics</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {getRecentTopics(student).map((topic, index) => (
                                            <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                                                {topic}
                                            </span>
                                        ))}
                                        {getRecentTopics(student).length === 0 && (
                                            <span className="text-sm text-muted-foreground">No recent topics</span>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <h4 className="text-sm font-medium text-muted-foreground uppercase mb-2">Strengths</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {getStrengths(student).map((strength, index) => (
                                            <span key={index} className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                                                {strength}
                                            </span>
                                        ))}
                                        {getStrengths(student).length === 0 && (
                                            <span className="text-sm text-muted-foreground">No strengths data available</span>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <h4 className="text-sm font-medium text-muted-foreground uppercase mb-2">Areas for Improvement</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {getWeaknesses(student).map((area, index) => (
                                            <span key={index} className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
                                                {area}
                                            </span>
                                        ))}
                                        {getWeaknesses(student).length === 0 && (
                                            <span className="text-sm text-muted-foreground">No improvement areas data available</span>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </>
                    )}

                    {activeTab === 'progress' && (
                        <>
                            {/* Test Performance Chart */}
                            <div className="mb-8">
                                <h4 className="text-sm font-medium text-muted-foreground uppercase mb-4">Test Performance</h4>
                                <div className="h-64 w-full">
                                    {getTestPerformanceData(student).length > 0 ? (
                                        <ResponsiveContainer width="100%" height="100%">
                                            <BarChart
                                                data={getTestPerformanceData(student)}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 25 }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis 
                                                    dataKey="name" 
                                                    angle={-45} 
                                                    textAnchor="end" 
                                                    height={60}
                                                    tick={{ fontSize: 10 }}
                                                    tickFormatter={topicFormatter}
                                                />
                                                <YAxis 
                                                    domain={[0, 100]} 
                                                    tickFormatter={percentFormatter} 
                                                />
                                                <Tooltip content={<TestPerformanceTooltip />} />
                                                <Legend />
                                                <Bar dataKey="score" name="Test Score" fill="hsl(var(--chart-primary))" />
                                                <Bar dataKey="proficiencyBefore" name="Proficiency Before" fill="hsl(var(--warning))" />
                                                <Bar dataKey="proficiencyAfter" name="Proficiency After" fill="hsl(var(--success))" />
                                            </BarChart>
                                        </ResponsiveContainer>
                                    ) : (
                                        <div className="h-full flex items-center justify-center bg-muted rounded-lg">
                                            <p className="text-muted-foreground">No test data available</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                            
                            {/* Activity Chart */}
                            <div>
                                <h4 className="text-sm font-medium text-muted-foreground uppercase mb-4">Daily Activity</h4>
                                <div className="h-64 w-full">
                                    {getProgressData(student).length > 0 ? (
                                        <ResponsiveContainer width="100%" height="100%">
                                            <LineChart
                                                data={getProgressData(student)}
                                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                            >
                                                <CartesianGrid strokeDasharray="3 3" />
                                                <XAxis dataKey="date" />
                                                <YAxis 
                                                    yAxisId="left" 
                                                    label={{ 
                                                        value: 'Count', 
                                                        angle: -90, 
                                                        position: 'insideLeft',
                                                        style: { textAnchor: 'middle' }
                                                    }}
                                                />
                                                <YAxis 
                                                    yAxisId="right" 
                                                    orientation="right" 
                                                    domain={[0, 100]} 
                                                    tickFormatter={percentFormatter}
                                                    label={{ 
                                                        value: 'Consistency (%)', 
                                                        angle: 90, 
                                                        position: 'insideRight',
                                                        style: { textAnchor: 'middle' }
                                                    }}
                                                />
                                                <Tooltip content={<ActivityTooltip />}/>
                                                <Legend />
                                                <Line yAxisId="left" type="monotone" dataKey="minutesSpent" name="Minutes Spent" stroke="hsl(var(--chart-primary))" activeDot={{ r: 8 }} />
                                                <Line yAxisId="left" type="monotone" dataKey="questionsAnswered" name="Questions Answered" stroke="hsl(var(--destructive))" />
                                                <Line yAxisId="right" type="monotone" dataKey="consistencyScore" name="Consistency Score" stroke="hsl(var(--success))" />
                                            </LineChart>
                                        </ResponsiveContainer>
                                    ) : (
                                        <div className="h-full flex items-center justify-center bg-muted rounded-lg">
                                            <p className="text-muted-foreground">No activity data available</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </>
                    )}

                    {activeTab === 'skills' && (
                        <>
                            {/* Radar Chart for Skills */}
                            <div className="h-96 w-full">
                                {getSkillRadarData(student).length > 0 ? (
                                    <ResponsiveContainer width="100%" height="100%">
                                        <RadarChart outerRadius="80%" data={getSkillRadarData(student)}>
                                            <PolarGrid />
                                            <PolarAngleAxis dataKey="subject" />
                                            <PolarRadiusAxis domain={[0, 100]} tickFormatter={percentFormatter} />
                                            <Radar name="Proficiency" dataKey="proficiency" stroke="hsl(var(--chart-primary))" fill="hsl(var(--chart-primary))" fillOpacity={0.6} />
                                            <Radar name="Confidence" dataKey="confidence" stroke="hsl(var(--success))" fill="hsl(var(--success))" fillOpacity={0.6} />
                                            <Legend />
                                            <Tooltip formatter={percentFormatter} />
                                        </RadarChart>
                                    </ResponsiveContainer>
                                ) : (
                                    <div className="h-full flex items-center justify-center bg-muted rounded-lg">
                                        <p className="text-muted-foreground">No skills data available</p>
                                    </div>
                                )}
                            </div>
                            
                            {/* Learning Behavior Analysis */}
                            <div className="mt-8">
                                <h4 className="text-sm font-medium text-muted-foreground uppercase mb-4">Learning Behavior</h4>
                                
                                {(() => {
                                    const currentSubject = currentClass?.subject?.toLowerCase() || '';
                                    const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === currentSubject);
                                    const learningBehavior = subjectData?.analytics?.learningBehavior;
                                    
                                    if (!learningBehavior) {
                                        return (
                                            <div className="p-6 text-center text-muted-foreground bg-muted rounded-lg">
                                                No learning behavior data available
                                            </div>
                                        );
                                    }
                                    
                                    return (
                                        <div className="grid grid-cols-2 gap-4">
                                            <div className="p-4 bg-muted rounded-lg">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <BoltIcon className="w-4 h-4 text-accent" />
                                                    <span className="text-sm text-muted-foreground">Speed vs. Accuracy</span>
                                                </div>
                                                <span className="text-lg font-semibold text-primary">
                                                    {learningBehavior.learningStyle?.speedVsAccuracy < 0.4 ? 'Prioritizes Accuracy' :
                                                     learningBehavior.learningStyle?.speedVsAccuracy > 0.6 ? 'Prioritizes Speed' :
                                                     'Balanced Approach'}
                                                </span>
                                            </div>

                                            <div className="p-4 bg-muted rounded-lg">
                                                <div className="flex items-center gap-2 mb-2">
                                                    <ClockIcon className="w-4 h-4 text-accent" />
                                                    <span className="text-sm text-muted-foreground">Reflection Time</span>
                                                </div>
                                                <span className="text-lg font-semibold text-primary">
                                                    {learningBehavior.learningStyle?.reflectionTime < 0.4 ? 'Quick Decisions' : 
                                                     learningBehavior.learningStyle?.reflectionTime > 0.6 ? 'Thoughtful Analysis' : 
                                                     'Moderate Reflection'}
                                                </span>
                                            </div>
                                            
                                            {/* Error Patterns */}
                                            {learningBehavior.errorPatterns && learningBehavior.errorPatterns.length > 0 && (
                                                <div className="col-span-2 p-4 bg-muted rounded-lg">
                                                    <h5 className="text-sm font-medium text-muted-foreground mb-2">Common Error Patterns</h5>
                                                    <ul className="space-y-2">
                                                        {learningBehavior.errorPatterns.map((pattern, index) => (
                                                            <li key={index} className="text-sm">
                                                                <span className="font-medium">{pattern.patternType}:</span> {pattern.commonMistakes.join(', ')}
                                                            </li>
                                                        ))}
                                                    </ul>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })()}
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>,
        document.body
    );
};

export default StudentAnalyticsModal; 