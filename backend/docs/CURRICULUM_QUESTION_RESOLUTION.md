# Curriculum and Question Resolution Enhancement

## Overview

The AegisAI chat system has been enhanced to resolve raw nodeids and question IDs to meaningful content, providing students and teachers with contextual curriculum information instead of just displaying database identifiers.

## Key Features

### 1. Curriculum Node Resolution
- **Before**: Raw nodeids like `ObjectId("507f1f77bcf86cd799439011")`
- **After**: Meaningful content with topic names, descriptions, and hierarchical relationships

### 2. Question Resolution
- **Before**: Raw question IDs with no context
- **After**: Complete question content including text, options, answers, and explanations

### 3. Privacy Protection Maintained
- Teacher access restrictions to student chat conversations remain intact
- All existing privacy protections are preserved

## Technical Implementation

### Enhanced Collections Support

The `mongoMcpService.js` now supports additional collections:

```javascript
// New collections added
case 'curriculumNodes':
  // Dynamic curriculum node model based on subject
  model = createKnowledgeGraphModel(subject);
  break;
case 'questions':
  // Dynamic question model based on subject/class
  model = createQuestionModel(collectionName);
  break;
case 'aiQuestions':
  model = AIQuestion;
  break;
case 'questionTemp':
  model = QuestionTemp;
  break;
```

### New Methods Added

#### `resolveCurriculumNodes(curriculumProgress, subject)`
Resolves curriculum nodeids to meaningful content:
- Topic names and descriptions
- Hierarchical relationships (Chapter > Subtopic)
- Learning status and proficiency
- Parent-child relationships in curriculum

#### `resolveQuestions(questionIds, subject, questionCollection)`
Resolves question IDs to complete question content:
- Question text and options
- Correct answers and explanations
- Topic and subtopic associations
- Difficulty levels and bloom taxonomy
- Images and multimedia content

#### `extractQuestionIdsFromTestHistory(detailedTestHistory, gradedResults)`
Extracts question IDs from various test history sources:
- Test questions arrays
- Response objects with question references
- Answer sheets from graded results
- Question bank references

#### `buildHierarchicalPath(node, nodeMap)`
Creates readable hierarchical paths:
- `"Chapter: Algebra > Subtopic: Linear Equations"`
- `"Chapter: Geometry"`

## Enhanced User Context

### Student Context Enhancement
```javascript
// New fields added to user context
userContext.resolvedCurriculumNodes = [
  {
    nodeId: "507f1f77bcf86cd799439011",
    status: "In Progress",
    proficiency: 75,
    resolvedContent: {
      name: "Linear Equations",
      description: "Solving equations with one variable",
      type: "Subtopic",
      hierarchicalPath: "Chapter: Algebra > Subtopic: Linear Equations"
    }
  }
];

userContext.resolvedQuestions = [
  {
    questionId: "507f1f77bcf86cd799439012",
    source: "aiQuestions",
    resolvedContent: {
      question: "Solve for x: 2x + 5 = 13",
      options: ["x = 4", "x = 6", "x = 8", "x = 9"],
      answer: "x = 4",
      solution: "Subtract 5 from both sides: 2x = 8, then divide by 2: x = 4",
      topic: "Linear Equations"
    }
  }
];
```

### Teacher Context Enhancement
```javascript
// Teachers get resolved content for class-wide analysis
teacherContext.resolvedCurriculumNodes = [...];
teacherContext.resolvedQuestions = [...];
```

## AI Prompt Enhancement

### Updated Instructions for Students
```
INSTRUCTIONS:
- The curriculum progress includes resolvedCurriculumNodes with meaningful topic names
- The resolvedQuestions provide actual question text, options, answers, and explanations
- Reference specific topics using resolved curriculum node names
- When discussing questions, use actual question content instead of just IDs
- Suggest next steps based on curriculum progress and resolved topic hierarchy
```

### Updated Instructions for Teachers
```
AVAILABLE DATA INCLUDES:
- Resolved curriculum nodes with meaningful topic names and hierarchical relationships
- Resolved questions with actual question text, options, answers, and explanations
- Curriculum progress mapped to actual topic content instead of raw nodeids
```

## Usage Examples

### Student Query: "What topics am I struggling with?"
**Before**: "You're struggling with nodeids 507f1f77bcf86cd799439011 and 507f1f77bcf86cd799439012"

**After**: "You're struggling with Linear Equations (Chapter: Algebra) and Quadratic Functions (Chapter: Advanced Algebra). Based on your recent test performance, I recommend focusing on the fundamentals of solving linear equations first."

### Teacher Query: "Which students need help with algebra?"
**Before**: "Students with low proficiency in nodeid 507f1f77bcf86cd799439011"

**After**: "3 students are struggling with Linear Equations (Chapter: Algebra). Sarah scored 45% on the recent test about solving equations like '2x + 5 = 13', while Mike and John had difficulty with word problems involving linear relationships."

## Testing

Run the curriculum resolution test:
```bash
node test/curriculumResolutionTest.js
```

This test verifies:
- Curriculum node resolution functionality
- Question resolution across multiple collections
- Question ID extraction from test history
- Enhanced user knowledge graph generation
- Collection support for new data models

## Benefits

1. **Better User Experience**: Students see meaningful topic names instead of database IDs
2. **Contextual Learning**: AI can reference actual question content and curriculum structure
3. **Improved Analytics**: Teachers get insights with resolved content for better decision-making
4. **Maintained Privacy**: All existing privacy protections remain intact
5. **Scalable Architecture**: Dynamic model support for different subjects and question collections

## Future Enhancements

- Add caching for frequently resolved nodes and questions
- Implement batch resolution for better performance
- Add support for multimedia content resolution
- Enhance hierarchical path building with more complex relationships
