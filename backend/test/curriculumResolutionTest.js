import mongoMcpService from '../services/mongoMcpService.js';
import mongoose from 'mongoose';
import { config } from 'dotenv';

config();

/**
 * Test script for curriculum and question resolution functionality
 * Run with: node test/curriculumResolutionTest.js
 */
async function testCurriculumResolution() {
  console.log('🧪 Testing Curriculum and Question Resolution...\n');

  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    const mongoUri = process.env.MONGODB_URI || process.env.DB_URI || process.env.MONGO_URI_TEST || process.env.MONGO_URI_PROD;
    if (!mongoUri) {
      console.log('❌ No MongoDB URI found in environment variables');
      console.log('Available env vars:', Object.keys(process.env).filter(key => key.includes('MONGO')));
      return;
    }
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Initialize MCP Service
    console.log('\n1️⃣ Testing MCP Service Initialization...');
    const initialized = await mongoMcpService.initialize();
    
    if (!initialized) {
      console.log('❌ MCP initialization failed');
      return;
    }
    
    console.log('✅ MCP Service initialized successfully');

    // Test 2: Test curriculum node resolution
    console.log('\n2️⃣ Testing Curriculum Node Resolution...');
    try {
      // Create mock curriculum progress data
      const mockCurriculumProgress = [
        {
          nodeId: new mongoose.Types.ObjectId(),
          nodeModel: 'CurriculumNode_Mathematics',
          status: 'In Progress',
          proficiency: 75,
          subject: 'Mathematics'
        }
      ];

      const resolvedNodes = await mongoMcpService.resolveCurriculumNodes(
        mockCurriculumProgress, 
        'Mathematics'
      );
      
      console.log('✅ Curriculum node resolution completed');
      console.log(`Resolved ${resolvedNodes.length} curriculum nodes`);
      
      if (resolvedNodes.length > 0) {
        console.log('Sample resolved node structure:', {
          nodeId: resolvedNodes[0].nodeId,
          status: resolvedNodes[0].status,
          hasResolvedContent: !!resolvedNodes[0].resolvedContent,
          contentKeys: resolvedNodes[0].resolvedContent ? Object.keys(resolvedNodes[0].resolvedContent) : []
        });
      }
    } catch (error) {
      console.log('⚠️  Curriculum node resolution test failed:', error.message);
    }

    // Test 3: Test enhanced question resolution with class context
    console.log('\n3️⃣ Testing Enhanced Question Resolution...');
    try {
      // Create mock question IDs
      const mockQuestionIds = [
        new mongoose.Types.ObjectId(),
        new mongoose.Types.ObjectId()
      ];

      const resolvedQuestions = await mongoMcpService.resolveQuestions(
        mockQuestionIds,
        'Mathematics',
        'Class 10'  // Include class context
      );

      console.log('✅ Enhanced question resolution completed');
      console.log(`Resolved ${resolvedQuestions.length} questions`);

      if (resolvedQuestions.length > 0) {
        console.log('Sample resolved question structure:', {
          questionId: resolvedQuestions[0].questionId,
          source: resolvedQuestions[0].source,
          priority: resolvedQuestions[0].priority,
          hasResolvedContent: !!resolvedQuestions[0].resolvedContent,
          contentKeys: resolvedQuestions[0].resolvedContent ? Object.keys(resolvedQuestions[0].resolvedContent) : [],
          searchedCollections: resolvedQuestions[0].resolvedContent?.searchedCollections?.length || 0
        });
      }
    } catch (error) {
      console.log('⚠️  Enhanced question resolution test failed:', error.message);
    }

    // Test 3b: Test collection name generation for school students
    console.log('\n3️⃣b Testing Collection Name Generation (School)...');
    try {
      const collections = mongoMcpService.generateQuestionCollectionNames('Mathematics', 'Class 10');
      console.log('✅ School collection name generation completed');
      console.log(`Generated ${collections.length} collection configurations`);
      console.log('Sample collections:', collections.slice(0, 3).map(c => ({
        collection: c.collection,
        collectionName: c.collectionName,
        priority: c.priority
      })));
    } catch (error) {
      console.log('⚠️  School collection name generation test failed:', error.message);
    }

    // Test 3c: Test collection name generation for UPSC
    console.log('\n3️⃣c Testing Collection Name Generation (UPSC)...');
    try {
      const upscCollections = mongoMcpService.generateQuestionCollectionNames('Economy', 'UPSC Prelims');
      console.log('✅ UPSC collection name generation completed');
      console.log(`Generated ${upscCollections.length} UPSC collection configurations`);
      console.log('UPSC collections:', upscCollections.slice(0, 5).map(c => ({
        collection: c.collection,
        collectionName: c.collectionName,
        priority: c.priority
      })));
    } catch (error) {
      console.log('⚠️  UPSC collection name generation test failed:', error.message);
    }

    // Test 3d: Test competitive exam detection
    console.log('\n3️⃣d Testing Competitive Exam Detection...');
    try {
      const isUpsc = mongoMcpService.isCompetitiveExam('Economy', 'UPSC Prelims');
      const isSchool = mongoMcpService.isCompetitiveExam('Mathematics', 'Class 10');
      console.log('✅ Competitive exam detection completed');
      console.log('UPSC detected:', isUpsc);
      console.log('School detected as competitive:', isSchool);

      if (isUpsc) {
        const examType = mongoMcpService.getExamType('Economy', 'UPSC Prelims');
        const examSubject = mongoMcpService.getExamSubject('Economy');
        console.log('Exam type:', examType);
        console.log('Exam subject:', examSubject);
      }
    } catch (error) {
      console.log('⚠️  Competitive exam detection test failed:', error.message);
    }

    // Test 4: Test question ID extraction from test history
    console.log('\n4️⃣ Testing Question ID Extraction...');
    try {
      const mockTestHistory = [
        {
          questions: [new mongoose.Types.ObjectId(), new mongoose.Types.ObjectId()],
          responses: [
            { questionId: new mongoose.Types.ObjectId() }
          ]
        }
      ];

      const mockGradedResults = [
        {
          answerSheets: [
            {
              responses: [
                { questionId: new mongoose.Types.ObjectId() }
              ]
            }
          ]
        }
      ];

      const extractedIds = mongoMcpService.extractQuestionIdsFromTestHistory(
        mockTestHistory,
        mockGradedResults
      );
      
      console.log('✅ Question ID extraction completed');
      console.log(`Extracted ${extractedIds.length} unique question IDs`);
    } catch (error) {
      console.log('⚠️  Question ID extraction test failed:', error.message);
    }

    // Test 5: Test enhanced user knowledge graph with resolution
    console.log('\n5️⃣ Testing Enhanced User Knowledge Graph...');
    try {
      // Use a test student ID - this will likely fail with mock data but shows the flow
      const testStudentId = new mongoose.Types.ObjectId();
      const testSubject = 'Mathematics';
      
      const userContext = await mongoMcpService.getUserKnowledgeGraph(testStudentId, testSubject);
      console.log('✅ Enhanced user knowledge graph retrieval completed');
      console.log('Context keys:', Object.keys(userContext));
      
      if (userContext.resolvedCurriculumNodes) {
        console.log(`✅ Found ${userContext.resolvedCurriculumNodes.length} resolved curriculum nodes`);
      }
      
      if (userContext.resolvedQuestions) {
        console.log(`✅ Found ${userContext.resolvedQuestions.length} resolved questions`);
      }
    } catch (error) {
      console.log('⚠️  Enhanced user knowledge graph test failed (expected with mock data):', error.message);
    }

    // Test 6: Test collection support
    console.log('\n6️⃣ Testing Collection Support...');
    try {
      // Test curriculum nodes collection
      const curriculumQuery = await mongoMcpService.executeQuery('find', {
        collection: 'curriculumNodes',
        query: { type: 'Chapter' },
        subject: 'Mathematics',
        limit: 1
      });
      console.log('✅ Curriculum nodes collection query successful');
      console.log(`Found ${curriculumQuery.content?.length || 0} curriculum nodes`);

      // Test questions collection
      const questionsQuery = await mongoMcpService.executeQuery('find', {
        collection: 'questions',
        query: {},
        questionCollection: 'questionBank_class10_mathematics',
        limit: 1
      });
      console.log('✅ Questions collection query successful');
      console.log(`Found ${questionsQuery.content?.length || 0} questions`);

      // Test AI questions collection
      const aiQuestionsQuery = await mongoMcpService.executeQuery('find', {
        collection: 'aiQuestions',
        query: {},
        limit: 1
      });
      console.log('✅ AI questions collection query successful');
      console.log(`Found ${aiQuestionsQuery.content?.length || 0} AI questions`);

    } catch (error) {
      console.log('⚠️  Collection support test failed:', error.message);
    }

    // Cleanup
    console.log('\n7️⃣ Cleanup...');
    await mongoMcpService.disconnect();
    await mongoose.disconnect();
    console.log('✅ Cleanup completed');

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }

  console.log('\n🏁 Curriculum Resolution Test Complete');
  console.log('\n📋 Summary:');
  console.log('- Curriculum nodeids can now be resolved to meaningful topic names and descriptions');
  console.log('- Question IDs can now be resolved to actual question content, options, and answers');
  console.log('- The AI agent now has access to contextual curriculum and question information');
  console.log('- Privacy protections remain in place for teacher access restrictions');
}

// Run the test
testCurriculumResolution().catch(console.error);
