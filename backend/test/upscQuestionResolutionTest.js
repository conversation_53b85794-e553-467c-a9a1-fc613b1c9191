import mongoMcpService from '../services/mongoMcpService.js';
import mongoose from 'mongoose';
import { config } from 'dotenv';

config();

/**
 * Test script specifically for UPSC question resolution
 * Run with: node test/upscQuestionResolutionTest.js
 */
async function testUpscQuestionResolution() {
  console.log('🧪 Testing UPSC Question Resolution...\n');

  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    const mongoUri = process.env.MONGODB_URI || process.env.DB_URI || process.env.MONGO_URI_TEST || process.env.MONGO_URI_PROD;
    if (!mongoUri) {
      console.log('❌ No MongoDB URI found in environment variables');
      return;
    }
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');

    // Initialize MCP Service
    console.log('\n1️⃣ Testing MCP Service Initialization...');
    const initialized = await mongoMcpService.initialize();
    
    if (!initialized) {
      console.log('❌ MCP initialization failed');
      return;
    }
    
    console.log('✅ MCP Service initialized successfully');

    // Test UPSC collection name generation
    console.log('\n2️⃣ Testing UPSC Collection Name Generation...');
    try {
      const upscCollections = mongoMcpService.generateQuestionCollectionNames('Economy', 'UPSC Prelims');
      console.log('✅ UPSC collection name generation completed');
      console.log(`Generated ${upscCollections.length} UPSC collection configurations`);
      
      console.log('\n📋 Generated UPSC Collections (in priority order):');
      upscCollections.forEach((config, index) => {
        console.log(`${index + 1}. Priority ${config.priority}: ${config.collectionName}`);
      });

      // Check if the correct collection name is included
      const correctCollection = upscCollections.find(c => c.collectionName === 'questionBank_class13_upsc-prelims');
      if (correctCollection) {
        console.log(`\n✅ Found correct collection: questionBank_class13_upsc-prelims (Priority: ${correctCollection.priority})`);
      } else {
        console.log('\n❌ Correct collection questionBank_class13_upsc-prelims not found!');
      }

    } catch (error) {
      console.log('⚠️  UPSC collection name generation test failed:', error.message);
    }

    // Test competitive exam detection
    console.log('\n3️⃣ Testing Competitive Exam Detection...');
    try {
      const testCases = [
        { subject: 'Economy', class: 'UPSC Prelims', expected: true },
        { subject: 'Mathematics', class: 'Class 10', expected: false },
        { subject: 'Polity', class: 'UPSC', expected: true },
        { subject: 'History', class: 'IAS Prelims', expected: true }
      ];

      testCases.forEach(testCase => {
        const isCompetitive = mongoMcpService.isCompetitiveExam(testCase.subject, testCase.class);
        const status = isCompetitive === testCase.expected ? '✅' : '❌';
        console.log(`${status} ${testCase.subject} + ${testCase.class}: ${isCompetitive} (expected: ${testCase.expected})`);
        
        if (isCompetitive) {
          const examType = mongoMcpService.getExamType(testCase.subject, testCase.class);
          const examSubject = mongoMcpService.getExamSubjectForCollection(testCase.subject, testCase.class);
          console.log(`   → Exam Type: ${examType}, Collection Subject: ${examSubject}`);
        }
      });

    } catch (error) {
      console.log('⚠️  Competitive exam detection test failed:', error.message);
    }

    // Test actual question resolution with UPSC context
    console.log('\n4️⃣ Testing UPSC Question Resolution...');
    try {
      // Create mock question IDs for testing
      const mockQuestionIds = [
        new mongoose.Types.ObjectId(),
        new mongoose.Types.ObjectId()
      ];

      const resolvedQuestions = await mongoMcpService.resolveQuestions(
        mockQuestionIds,
        'Economy',
        'UPSC Prelims'
      );
      
      console.log('✅ UPSC question resolution completed');
      console.log(`Attempted to resolve ${mockQuestionIds.length} questions`);
      console.log(`Resolution results: ${resolvedQuestions.length} entries`);
      
      if (resolvedQuestions.length > 0) {
        const firstResult = resolvedQuestions[0];
        console.log('\n📋 Sample resolution result:');
        console.log(`Question ID: ${firstResult.questionId}`);
        console.log(`Source: ${firstResult.source}`);
        console.log(`Priority: ${firstResult.priority}`);
        
        if (firstResult.resolvedContent?.searchedCollections) {
          console.log(`Collections searched: ${firstResult.resolvedContent.searchedCollections.length}`);
          console.log('First 5 collections searched:');
          firstResult.resolvedContent.searchedCollections.slice(0, 5).forEach((collection, index) => {
            console.log(`  ${index + 1}. ${collection}`);
          });
        }
      }

    } catch (error) {
      console.log('⚠️  UPSC question resolution test failed:', error.message);
    }

    // Test question ID extraction patterns
    console.log('\n5️⃣ Testing Question ID Extraction Patterns...');
    try {
      const mockTestHistory = [
        {
          questions: [new mongoose.Types.ObjectId()],
          responses: [
            { questionId: new mongoose.Types.ObjectId() },
            { question_id: new mongoose.Types.ObjectId() },
            { question: { _id: new mongoose.Types.ObjectId() } }
          ],
          testQuestions: [
            { _id: new mongoose.Types.ObjectId() },
            { questionId: new mongoose.Types.ObjectId() }
          ]
        }
      ];

      const mockGradedResults = [
        {
          answerSheets: [
            {
              responses: [
                { questionId: new mongoose.Types.ObjectId() },
                { question: { _id: new mongoose.Types.ObjectId() } }
              ],
              questions: [new mongoose.Types.ObjectId()]
            }
          ],
          questions: [new mongoose.Types.ObjectId()]
        }
      ];

      const extractedIds = mongoMcpService.extractQuestionIdsFromTestHistory(
        mockTestHistory,
        mockGradedResults
      );
      
      console.log('✅ Question ID extraction completed');
      console.log(`Extracted ${extractedIds.length} unique question IDs from enhanced patterns`);

    } catch (error) {
      console.log('⚠️  Question ID extraction test failed:', error.message);
    }

    // Cleanup
    console.log('\n6️⃣ Cleanup...');
    await mongoMcpService.disconnect();
    await mongoose.disconnect();
    console.log('✅ Cleanup completed');

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }

  console.log('\n🏁 UPSC Question Resolution Test Complete');
  console.log('\n📋 Summary:');
  console.log('- Enhanced collection name generation for UPSC (questionBank_class13_upsc-prelims)');
  console.log('- Improved competitive exam detection');
  console.log('- Enhanced question ID extraction patterns');
  console.log('- Priority-based collection searching for UPSC questions');
}

// Run the test
testUpscQuestionResolution().catch(console.error);
