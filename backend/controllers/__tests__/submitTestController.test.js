import { describe, it, expect, beforeEach, vi } from 'vitest';
import { submitTest } from '../submitTestController.js';
import * as testService from '../../services/testService.js';
import Student from '../../models/Student.js';
import TestHistory from '../../models/TestHistory.js';

// Mock all dependencies
vi.mock('../../services/testService.js');
vi.mock('../../models/Student.js');
vi.mock('../../models/TestHistory.js');
vi.mock('../../services/classAnalyticsService.js');
vi.mock('../../services/studentAnalyticsService.js');

describe('submitTestController', () => {
  let req, res;

  beforeEach(() => {
    vi.clearAllMocks();
    
    req = {
      body: {
        userId: 'student123',
        testId: 'test123',
        responses: [
          {
            questionId: 'q1',
            selectedAnswer: 'A',
            intuition: 'This is correct',
            timeSpent: 30,
          },
          {
            questionId: 'q2',
            selectedAnswer: 'B',
            intuition: 'Not sure about this',
            timeSpent: 45,
          },
        ],
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
        subject: 'Mathematics',
      },
    };

    res = {
      json: vi.fn(),
      status: vi.fn().mockReturnThis(),
    };
  });

  describe('Adaptive Test Processing', () => {
    beforeEach(() => {
      // Mock test config for adaptive test
      vi.mocked(testService.getTestConfig).mockResolvedValue({
        testId: 'test123',
        isAdaptive: true,
        allowBatchProcessing: false,
        maxUsersForRealTime: 100,
        diagnosticTest: false,
      });

      vi.mocked(testService.getActiveUserCount).mockResolvedValue(5);
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      vi.mocked(testService.evaluateResponses).mockResolvedValue([
        {
          questionId: 'q1',
          selectedAnswer: 'A',
          isCorrect: true,
          intuition: 'This is correct',
          timeSpent: 30,
        },
        {
          questionId: 'q2',
          selectedAnswer: 'B',
          isCorrect: false,
          intuition: 'Not sure about this',
          timeSpent: 45,
        },
      ]);
    });

    it('processes adaptive test successfully', async () => {
      // Mock all required service functions
      vi.mocked(testService.getUserSubject).mockResolvedValue({
        subjectName: 'Mathematics',
        proficiency: 0.7,
        subskills: [
          { name: 'Algebra', proficiency: 0.8 },
          { name: 'Geometry', proficiency: 0.6 },
        ],
      });

      vi.mocked(testService.getQuestion).mockResolvedValue({
        _id: 'q1',
        subskills: ['Algebra'],
        difficulty: 0.5,
      });

      vi.mocked(testService.updateUserProficiency).mockResolvedValue(0.75);
      vi.mocked(testService.updateSubskillProficiencies).mockResolvedValue({
        Algebra: 0.85,
        Geometry: 0.6,
      });

      // Mock Student.findOneAndUpdate
      vi.mocked(Student.findOneAndUpdate).mockResolvedValue({
        _id: 'student123',
        subjects: [
          {
            subjectName: 'Mathematics',
            attemptedTests: [],
          },
        ],
      });

      await submitTest(req, res);

      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Adaptive test submitted successfully.',
        newProficiency: {
          subskills: expect.any(Object),
        },
      });
    });

    it('handles errors in adaptive test processing', async () => {
      vi.mocked(testService.getUserSubject).mockRejectedValue(new Error('Database error'));

      await submitTest(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: expect.stringContaining('error'),
      });
    });

    it('updates student attempted tests correctly', async () => {
      vi.mocked(testService.getUserSubject).mockResolvedValue({
        subjectName: 'Mathematics',
        proficiency: 0.7,
        subskills: [],
      });

      vi.mocked(testService.getQuestion).mockResolvedValue({
        _id: 'q1',
        subskills: ['Algebra'],
        difficulty: 0.5,
      });

      vi.mocked(testService.updateUserProficiency).mockResolvedValue(0.75);
      vi.mocked(testService.updateSubskillProficiencies).mockResolvedValue({});

      vi.mocked(Student.findOneAndUpdate).mockResolvedValue({
        _id: 'student123',
        subjects: [{ subjectName: 'Mathematics', attemptedTests: [] }],
      });

      await submitTest(req, res);

      expect(Student.findOneAndUpdate).toHaveBeenCalledWith(
        {
          _id: 'student123',
          'subjects.subjectName': { $regex: '^Mathematics$', $options: 'i' },
        },
        { $push: { 'subjects.$.attemptedTests': expect.any(Object) } },
        { new: true }
      );
    });
  });

  describe('Batch Test Processing', () => {
    beforeEach(() => {
      // Mock test config for batch processing
      vi.mocked(testService.getTestConfig).mockResolvedValue({
        testId: 'test123',
        isAdaptive: false,
        allowBatchProcessing: true,
        maxUsersForRealTime: 10,
        diagnosticTest: false,
      });

      vi.mocked(testService.getActiveUserCount).mockResolvedValue(15);
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      vi.mocked(testService.evaluateResponses).mockResolvedValue([
        {
          questionId: 'q1',
          selectedAnswer: 'A',
          isCorrect: true,
          intuition: 'This is correct',
          timeSpent: 30,
        },
      ]);
    });

    it('processes batch test successfully', async () => {
      vi.mocked(testService.processBatchTest).mockResolvedValue({
        overall: 0.8,
        subskills: { Algebra: 0.85 },
      });

      await submitTest(req, res);

      expect(testService.processBatchTest).toHaveBeenCalledWith(
        'student123',
        expect.any(Array),
        'Mathematics'
      );

      expect(res.json).toHaveBeenCalledWith({
        success: true,
        message: 'Batch test submitted successfully.',
        newProficiency: {
          overall: 0.8,
          subskills: { Algebra: 0.85 },
        },
      });
    });

    it('handles batch processing errors', async () => {
      vi.mocked(testService.processBatchTest).mockRejectedValue(new Error('Batch processing failed'));

      await submitTest(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: expect.stringContaining('error'),
      });
    });
  });

  describe('Invalid Test Configuration', () => {
    it('rejects invalid test configuration', async () => {
      vi.mocked(testService.getTestConfig).mockResolvedValue({
        testId: 'test123',
        isAdaptive: false,
        allowBatchProcessing: false,
        maxUsersForRealTime: 5,
        diagnosticTest: false,
      });

      vi.mocked(testService.getActiveUserCount).mockResolvedValue(10);
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      vi.mocked(testService.evaluateResponses).mockResolvedValue([]);

      await submitTest(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid test configuration or too many active users.',
      });
    });
  });

  describe('Response Evaluation', () => {
    it('evaluates responses correctly', async () => {
      vi.mocked(testService.getTestConfig).mockResolvedValue({
        testId: 'test123',
        isAdaptive: false,
        allowBatchProcessing: true,
        maxUsersForRealTime: 10,
        diagnosticTest: false,
      });

      vi.mocked(testService.getActiveUserCount).mockResolvedValue(5);
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      
      const evaluatedResponses = [
        {
          questionId: 'q1',
          selectedAnswer: 'A',
          isCorrect: true,
          intuition: 'This is correct',
          timeSpent: 30,
        },
        {
          questionId: 'q2',
          selectedAnswer: 'B',
          isCorrect: false,
          intuition: 'Not sure about this',
          timeSpent: 45,
        },
      ];

      vi.mocked(testService.evaluateResponses).mockResolvedValue(evaluatedResponses);
      vi.mocked(testService.processBatchTest).mockResolvedValue({ overall: 0.5 });

      await submitTest(req, res);

      expect(testService.evaluateResponses).toHaveBeenCalledWith(
        'questionBank_class10_mathematics',
        req.body.responses
      );
    });
  });

  describe('Error Handling', () => {
    it('handles missing required fields', async () => {
      req.body = {}; // Empty body

      await submitTest(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: expect.stringContaining('error'),
      });
    });

    it('handles test config retrieval errors', async () => {
      vi.mocked(testService.getTestConfig).mockRejectedValue(new Error('Config not found'));
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      vi.mocked(testService.evaluateResponses).mockResolvedValue([]);

      await submitTest(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: expect.stringContaining('error'),
      });
    });

    it('handles student not found error', async () => {
      vi.mocked(testService.getTestConfig).mockResolvedValue({
        testId: 'test123',
        isAdaptive: true,
        allowBatchProcessing: false,
        maxUsersForRealTime: 100,
        diagnosticTest: false,
      });

      vi.mocked(testService.getActiveUserCount).mockResolvedValue(5);
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      vi.mocked(testService.evaluateResponses).mockResolvedValue([]);
      vi.mocked(testService.getUserSubject).mockResolvedValue({});
      vi.mocked(testService.getQuestion).mockResolvedValue({});
      vi.mocked(testService.updateUserProficiency).mockResolvedValue(0.5);
      vi.mocked(testService.updateSubskillProficiencies).mockResolvedValue({});

      // Mock student not found
      vi.mocked(Student.findOneAndUpdate).mockResolvedValue(null);

      await submitTest(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Student or subject not found',
      });
    });
  });

  describe('Time Tracking', () => {
    it('calculates total time taken correctly', async () => {
      vi.mocked(testService.getTestConfig).mockResolvedValue({
        testId: 'test123',
        isAdaptive: true,
        allowBatchProcessing: false,
        maxUsersForRealTime: 100,
        diagnosticTest: false,
      });

      vi.mocked(testService.getActiveUserCount).mockResolvedValue(5);
      vi.mocked(testService.getCollectionNameTest).mockResolvedValue('questionBank_class10_mathematics');
      vi.mocked(testService.evaluateResponses).mockResolvedValue([]);
      vi.mocked(testService.getUserSubject).mockResolvedValue({});
      vi.mocked(testService.getQuestion).mockResolvedValue({});
      vi.mocked(testService.updateUserProficiency).mockResolvedValue(0.5);
      vi.mocked(testService.updateSubskillProficiencies).mockResolvedValue({});

      vi.mocked(Student.findOneAndUpdate).mockResolvedValue({
        _id: 'student123',
        subjects: [{ subjectName: 'Mathematics', attemptedTests: [] }],
      });

      await submitTest(req, res);

      expect(Student.findOneAndUpdate).toHaveBeenCalledWith(
        expect.any(Object),
        {
          $push: {
            'subjects.$.attemptedTests': expect.objectContaining({
              startTime: new Date('2024-01-15T10:00:00Z'),
              endTime: new Date('2024-01-15T10:30:00Z'),
              totalTimeTaken: 1800, // 30 minutes in seconds
            }),
          },
        },
        expect.any(Object)
      );
    });
  });
});
