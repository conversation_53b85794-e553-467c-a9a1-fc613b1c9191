import { config } from 'dotenv';
import mongoose from 'mongoose';
import Student from '../models/Student.js';
import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import ChatConversation from '../models/ChatConversation.js';
import TestHistory from '../models/TestHistory.js';
import AegisGrader from '../models/AegisGrader.js';
import Class from '../models/Class.js';
import { createKnowledgeGraphModel } from '../models/knowledgeGraphModel.js';
import { createQuestionModel } from '../models/Question.js';
import AIQuestion from '../models/AIQuestions.js';
import QuestionTemp from '../models/QuestionTemp.js';

config();

/**
 * MongoDB MCP Service
 * Provides direct database access to LLMs through simplified MongoDB queries
 * Uses direct MongoDB queries as a reliable fallback approach
 */
class MongoMcpService {
  constructor() {
    this.isConnected = false;
    this.connectionString = process.env.MONGODB_URI || process.env.DB_URI || process.env.MONGO_URI_TEST || process.env.MONGO_URI_PROD;
  }

  /**
   * Initialize the MongoDB MCP service
   */
  async initialize() {
    try {
      console.log('[MCP] Initializing MongoDB direct access service...');
      
      // Check if MongoDB is connected
      if (mongoose.connection.readyState === 1) {
        this.isConnected = true;
        console.log('[MCP] MongoDB direct access service initialized successfully');
        return true;
      } else {
        console.warn('[MCP] MongoDB not connected, waiting for connection...');
        // Wait for MongoDB connection
        await new Promise((resolve, reject) => {
          if (mongoose.connection.readyState === 1) {
            resolve();
          } else {
            mongoose.connection.once('connected', resolve);
            mongoose.connection.once('error', reject);
            setTimeout(() => reject(new Error('MongoDB connection timeout')), 10000);
          }
        });
        
        this.isConnected = true;
        console.log('[MCP] MongoDB direct access service initialized successfully');
        return true;
      }
    } catch (error) {
      console.error('[MCP] Failed to initialize MongoDB service:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Execute a MongoDB query directly with privacy protection
   * @param {string} operation - The operation to execute
   * @param {object} queryArgs - Arguments for the operation
   * @param {object} context - Context for privacy validation (optional)
   * @returns {Promise<object>} - Query result
   */
  async executeQuery(operation, queryArgs = {}, context = {}) {
    if (!this.isConnected) {
      throw new Error('MCP service not connected. Call initialize() first.');
    }

    try {
      console.log(`[MCP] Executing direct query: ${operation} on ${queryArgs.collection}`);

      // PRIVACY PROTECTION: Prevent teacher access to student chat conversations
      if (queryArgs.collection === 'chatConversations' && context.isTeacherRequest) {
        console.warn(`[PRIVACY] Blocked teacher access to chatConversations collection`);
        throw new Error('Access denied: Teachers cannot access student chat conversations for privacy protection');
      }

      if (operation === 'find') {
        const { collection, query, limit, projection, sort, subject } = queryArgs;

        let model;
        switch (collection) {
          case 'students':
            model = Student;
            break;
          case 'studentKnowledgeGraph':
            model = StudentCurriculumModel;
            break;
          case 'chatConversations':
            model = ChatConversation;
            break;
          case 'testHistory':
            model = TestHistory;
            break;
          case 'aegisGrader':
            model = AegisGrader;
            break;
          case 'classes':
            model = Class;
            break;
          case 'curriculumNodes':
            // Dynamic curriculum node model based on subject
            if (!subject) {
              throw new Error('Subject parameter required for curriculumNodes collection');
            }
            model = createKnowledgeGraphModel(subject);
            break;
          case 'questions':
            // Dynamic question model based on subject/class
            const collectionName = queryArgs.questionCollection || 'questionBank_class10_mathematics';
            model = createQuestionModel(collectionName);
            break;
          case 'aiQuestions':
            model = AIQuestion;
            break;
          case 'questionTemp':
            model = QuestionTemp;
            break;
          default:
            throw new Error(`Unsupported collection: ${collection}`);
        }

        let queryBuilder = model.find(query);
        
        if (projection) queryBuilder = queryBuilder.select(projection);
        if (sort) queryBuilder = queryBuilder.sort(sort);
        if (limit) queryBuilder = queryBuilder.limit(limit);
        
        const results = await queryBuilder.exec();
        
        return {
          content: results,
          success: true
        };
      }
      
      throw new Error(`Unsupported operation: ${operation}`);
    } catch (error) {
      console.error(`[MCP] Error executing query:`, error);
      throw error;
    }
  }

  /**
   * Get comprehensive user knowledge graph data
   * @param {string} studentId - Student ID
   * @param {string} subject - Subject name
   * @returns {Promise<object>} - Complete user context
   */
  async getUserKnowledgeGraph(studentId, subject) {
    try {
      const userContext = {};

      // Get student profile with complete test history
      const studentData = await this.executeQuery('find', {
        collection: 'students',
        query: { _id: studentId },
        limit: 1
      });
      userContext.studentProfile = studentData.content?.[0] || null;

      // Extract test history for the specific subject
      if (userContext.studentProfile?.subjects) {
        const subjectData = userContext.studentProfile.subjects.find(s => s.subject === subject);
        userContext.testHistory = {
          subjectTests: subjectData?.tests || [],
          overallStats: {
            totalTests: subjectData?.tests?.length || 0,
            averageScore: this.calculateAverageScore(subjectData?.tests || []),
            recentPerformance: this.getRecentPerformanceTrend(subjectData?.tests || []),
            strongTopics: this.getStrongTopics(subjectData?.tests || []),
            weakTopics: this.getWeakTopics(subjectData?.tests || [])
          }
        };
      }

      // Get curriculum progress
      const curriculumData = await this.executeQuery('find', {
        collection: 'studentKnowledgeGraph',
        query: { studentId, subject },
        limit: 1
      });
      userContext.curriculumProgress = curriculumData.content?.[0] || null;

      // Resolve curriculum nodeids to meaningful content
      if (userContext.curriculumProgress?.curriculumProgress?.length > 0) {
        userContext.resolvedCurriculumNodes = await this.resolveCurriculumNodes(
          userContext.curriculumProgress.curriculumProgress,
          subject
        );
      }

      // Get recent chat conversations for context
      const chatHistory = await this.executeQuery('find', {
        collection: 'chatConversations',
        query: { userId: studentId, subject },
        sort: { updatedAt: -1 },
        limit: 3
      });
      userContext.recentConversations = chatHistory.content || [];

      // Get comprehensive test history from TestHistory collection
      if (userContext.studentProfile?.subjects) {
        const subjectData = userContext.studentProfile.subjects.find(s => s.subject === subject);
        if (subjectData?.testHistory?.length > 0) {
          const testHistoryData = await this.executeQuery('find', {
            collection: 'testHistory',
            query: { _id: { $in: subjectData.testHistory } },
            sort: { testDate: -1 },
            limit: 10
          });
          userContext.detailedTestHistory = testHistoryData.content || [];
        }
      }

      // Get graded test results from AegisGrader
      const gradedResults = await this.executeQuery('find', {
        collection: 'aegisGrader',
        query: {
          'answerSheets.rollNumber': userContext.studentProfile?.admissionNumber || '',
          'testDetails.subject': subject
        },
        sort: { 'testDetails.date': -1 },
        limit: 5
      });
      userContext.gradedTestResults = gradedResults.content || [];

      // Get detailed test analytics
      userContext.testAnalytics = this.generateTestAnalytics(userContext.testHistory?.subjectTests || []);

      // Generate comprehensive test insights
      userContext.comprehensiveTestInsights = this.generateComprehensiveTestInsights(
        userContext.testHistory?.subjectTests || [],
        userContext.detailedTestHistory || [],
        userContext.gradedTestResults || []
      );

      // Resolve question IDs from test history to meaningful content
      const allQuestionIds = this.extractQuestionIdsFromTestHistory(
        userContext.detailedTestHistory || [],
        userContext.gradedTestResults || []
      );

      if (allQuestionIds.length > 0) {
        // Extract student's class from profile for better question resolution
        const studentClass = userContext.studentProfile?.class || userContext.studentProfile?.className || null;
        userContext.resolvedQuestions = await this.resolveQuestions(allQuestionIds, subject, studentClass);
      }

      console.log('[MCP] Retrieved comprehensive user knowledge graph with complete test history and resolved content');
      return userContext;
    } catch (error) {
      console.error('[MCP] Error retrieving user knowledge graph:', error);
      throw error;
    }
  }

  /**
   * Generate MCP-enhanced prompt for Gemini
   * @param {string} studentId - Student ID
   * @param {string} subject - Subject name
   * @param {string} userMessage - User's message
   * @param {Array} conversationHistory - Previous conversation
   * @returns {Promise<string>} - Enhanced prompt with direct DB context
   */
  async generateEnhancedPrompt(studentId, subject, userMessage, conversationHistory = []) {
    try {
      // Get comprehensive user context through direct MongoDB access
      const userContext = await this.getUserKnowledgeGraph(studentId, subject);

      // Create enhanced prompt with direct database context
      let prompt = `You are an AI teaching assistant with direct access to the student's complete academic profile. 

STUDENT CONTEXT (Retrieved directly from database):
${JSON.stringify(userContext, null, 2)}

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.type}: ${msg.content}`).join('\n')}

CURRENT QUESTION: ${userMessage}

INSTRUCTIONS:
- You have access to the student's complete knowledge graph, test history, and learning progress
- The curriculum progress includes resolvedCurriculumNodes with meaningful topic names, descriptions, and hierarchical relationships
- The resolvedQuestions provide actual question text, options, answers, and explanations for referenced question IDs
- Provide personalized responses based on their actual performance data
- Reference specific topics they've struggled with or excelled in using the resolved curriculum node names
- When discussing questions, use the actual question content from resolvedQuestions instead of just IDs
- Suggest next steps based on their curriculum progress and resolved topic hierarchy
- Keep responses conversational but educational

Please provide a helpful, personalized response:`;

      return prompt;
    } catch (error) {
      console.error('[MCP] Error generating enhanced prompt:', error);
      // Fallback to basic prompt if MCP fails
      return `You are an AI teaching assistant helping a student learn ${subject}. 

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.type}: ${msg.content}`).join('\n')}

CURRENT QUESTION: ${userMessage}

Please provide a helpful response:`;
    }
  }

  /**
   * Disconnect from MCP service
   */
  async disconnect() {
    try {
      if (this.isConnected) {
        this.isConnected = false;
        console.log('[MCP] MongoDB direct access service disconnected');
      }
    } catch (error) {
      console.error('[MCP] Error disconnecting from service:', error);
    }
  }

  /**
   * Check if MCP service is ready
   */
  isReady() {
    return this.isConnected && mongoose.connection.readyState === 1;
  }

  /**
   * Calculate average score from test history
   * @param {Array} tests - Array of test objects
   * @returns {number} - Average score
   */
  calculateAverageScore(tests) {
    if (!tests || tests.length === 0) return 0;
    const totalScore = tests.reduce((sum, test) => sum + (test.score || 0), 0);
    return Math.round((totalScore / tests.length) * 100) / 100;
  }

  /**
   * Get recent performance trend
   * @param {Array} tests - Array of test objects
   * @returns {string} - Performance trend
   */
  getRecentPerformanceTrend(tests) {
    if (!tests || tests.length < 2) return 'insufficient_data';

    const recentTests = tests.slice(-3); // Last 3 tests
    const scores = recentTests.map(test => test.score || 0);

    if (scores.length < 2) return 'insufficient_data';

    const trend = scores[scores.length - 1] - scores[0];
    if (trend > 5) return 'improving';
    if (trend < -5) return 'declining';
    return 'stable';
  }

  /**
   * Identify strong topics from test history
   * @param {Array} tests - Array of test objects
   * @returns {Array} - Topics with high performance
   */
  getStrongTopics(tests) {
    if (!tests || tests.length === 0) return [];

    const topicScores = {};
    tests.forEach(test => {
      if (test.topic && test.score >= 80) {
        topicScores[test.topic] = (topicScores[test.topic] || []).concat(test.score);
      }
    });

    return Object.keys(topicScores)
      .filter(topic => {
        const scores = topicScores[topic];
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        return avgScore >= 80;
      })
      .slice(0, 5); // Top 5 strong topics
  }

  /**
   * Identify weak topics from test history
   * @param {Array} tests - Array of test objects
   * @returns {Array} - Topics needing improvement
   */
  getWeakTopics(tests) {
    if (!tests || tests.length === 0) return [];

    const topicScores = {};
    tests.forEach(test => {
      if (test.topic && test.score < 70) {
        topicScores[test.topic] = (topicScores[test.topic] || []).concat(test.score);
      }
    });

    return Object.keys(topicScores)
      .filter(topic => {
        const scores = topicScores[topic];
        const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
        return avgScore < 70;
      })
      .slice(0, 5); // Top 5 weak topics
  }

  /**
   * Generate comprehensive test analytics
   * @param {Array} tests - Array of test objects
   * @returns {object} - Detailed analytics
   */
  generateTestAnalytics(tests) {
    if (!tests || tests.length === 0) {
      return {
        hasData: false,
        message: 'No test history available'
      };
    }

    const analytics = {
      hasData: true,
      totalTests: tests.length,
      dateRange: {
        earliest: tests[0]?.date || tests[0]?.createdAt,
        latest: tests[tests.length - 1]?.date || tests[tests.length - 1]?.createdAt
      },
      scoreDistribution: {
        excellent: tests.filter(t => t.score >= 90).length,
        good: tests.filter(t => t.score >= 80 && t.score < 90).length,
        average: tests.filter(t => t.score >= 70 && t.score < 80).length,
        needsImprovement: tests.filter(t => t.score < 70).length
      },
      recentTests: tests.slice(-5), // Last 5 tests
      topicPerformance: this.analyzeTopicPerformance(tests),
      learningVelocity: this.calculateLearningVelocity(tests)
    };

    return analytics;
  }

  /**
   * Analyze performance by topic
   * @param {Array} tests - Array of test objects
   * @returns {object} - Topic-wise performance
   */
  analyzeTopicPerformance(tests) {
    const topicData = {};

    tests.forEach(test => {
      if (test.topic) {
        if (!topicData[test.topic]) {
          topicData[test.topic] = {
            scores: [],
            attempts: 0,
            averageScore: 0
          };
        }
        topicData[test.topic].scores.push(test.score || 0);
        topicData[test.topic].attempts++;
      }
    });

    // Calculate averages
    Object.keys(topicData).forEach(topic => {
      const scores = topicData[topic].scores;
      topicData[topic].averageScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    });

    return topicData;
  }

  /**
   * Calculate learning velocity (improvement over time)
   * @param {Array} tests - Array of test objects
   * @returns {object} - Learning velocity metrics
   */
  calculateLearningVelocity(tests) {
    if (tests.length < 3) {
      return { status: 'insufficient_data' };
    }

    const recentTests = tests.slice(-6); // Last 6 tests
    const scores = recentTests.map(test => test.score || 0);

    // Simple linear regression to find trend
    const n = scores.length;
    const sumX = (n * (n + 1)) / 2;
    const sumY = scores.reduce((a, b) => a + b, 0);
    const sumXY = scores.reduce((sum, score, index) => sum + score * (index + 1), 0);
    const sumX2 = (n * (n + 1) * (2 * n + 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);

    return {
      status: 'calculated',
      trend: slope > 1 ? 'fast_improvement' : slope > 0 ? 'gradual_improvement' : slope < -1 ? 'declining' : 'stable',
      slopeValue: Math.round(slope * 100) / 100,
      interpretation: this.interpretLearningVelocity(slope)
    };
  }

  /**
   * Interpret learning velocity slope
   * @param {number} slope - Learning velocity slope
   * @returns {string} - Human-readable interpretation
   */
  interpretLearningVelocity(slope) {
    if (slope > 2) return 'Excellent progress - scores improving rapidly';
    if (slope > 1) return 'Good progress - steady improvement';
    if (slope > 0) return 'Gradual improvement - keep practicing';
    if (slope > -1) return 'Stable performance - focus on consistency';
    return 'Declining performance - needs attention';
  }

  /**
   * Generate comprehensive test insights from all test data sources
   * @param {Array} subjectTests - Tests from student profile
   * @param {Array} detailedTestHistory - Tests from TestHistory collection
   * @param {Array} gradedResults - Results from AegisGrader
   * @returns {object} - Comprehensive test insights
   */
  generateComprehensiveTestInsights(subjectTests, detailedTestHistory, gradedResults) {
    const insights = {
      totalTestsAcrossAllSources: 0,
      testSourceBreakdown: {
        profileTests: subjectTests.length,
        detailedTests: detailedTestHistory.length,
        gradedTests: gradedResults.length
      },
      performanceOverTime: [],
      questionLevelAnalysis: [],
      testTypePerformance: {},
      timeManagementInsights: {},
      difficultyProgression: {},
      recentPerformanceTrend: 'stable'
    };

    // Combine all test data
    const allTests = [
      ...subjectTests.map(test => ({ ...test, source: 'profile' })),
      ...detailedTestHistory.map(test => ({ ...test, source: 'detailed' })),
      ...gradedResults.flatMap(result =>
        result.answerSheets?.map(sheet => ({
          ...sheet,
          testDetails: result.testDetails,
          source: 'graded'
        })) || []
      )
    ];

    insights.totalTestsAcrossAllSources = allTests.length;

    if (allTests.length === 0) {
      insights.message = 'No test data available across any source';
      return insights;
    }

    // Performance over time analysis
    insights.performanceOverTime = allTests
      .filter(test => test.score || test.totalScore || test.evaluationResult?.totalScore)
      .map(test => ({
        date: test.date || test.testDate || test.endTime,
        score: test.score || test.totalScore || test.evaluationResult?.totalScore,
        testType: test.testType || 'unknown',
        source: test.source
      }))
      .sort((a, b) => new Date(a.date) - new Date(b.date));

    // Question-level analysis (from detailed responses)
    insights.questionLevelAnalysis = this.analyzeQuestionLevelPerformance(allTests);

    // Test type performance
    insights.testTypePerformance = this.analyzeTestTypePerformance(allTests);

    // Time management insights
    insights.timeManagementInsights = this.analyzeTimeManagement(allTests);

    // Difficulty progression
    insights.difficultyProgression = this.analyzeDifficultyProgression(allTests);

    // Recent performance trend
    if (insights.performanceOverTime.length >= 3) {
      const recentScores = insights.performanceOverTime.slice(-3).map(p => p.score);
      const trend = recentScores[recentScores.length - 1] - recentScores[0];
      insights.recentPerformanceTrend = trend > 5 ? 'improving' : trend < -5 ? 'declining' : 'stable';
    }

    return insights;
  }

  /**
   * Analyze question-level performance patterns
   * @param {Array} allTests - Combined test data
   * @returns {object} - Question-level insights
   */
  analyzeQuestionLevelPerformance(allTests) {
    const questionAnalysis = {
      totalQuestionsAttempted: 0,
      averageTimePerQuestion: 0,
      accuracyByDifficulty: {},
      commonErrorPatterns: [],
      strongQuestionTypes: [],
      weakQuestionTypes: []
    };

    const questionsWithResponses = allTests.filter(test => test.responses?.length > 0);

    if (questionsWithResponses.length === 0) {
      return { message: 'No detailed question-level data available' };
    }

    let totalQuestions = 0;
    let totalTime = 0;
    const difficultyAccuracy = {};

    questionsWithResponses.forEach(test => {
      test.responses.forEach(response => {
        totalQuestions++;
        totalTime += response.timeSpent || 0;

        // Track accuracy by difficulty (if available)
        const difficulty = response.difficulty || 'unknown';
        if (!difficultyAccuracy[difficulty]) {
          difficultyAccuracy[difficulty] = { correct: 0, total: 0 };
        }
        difficultyAccuracy[difficulty].total++;
        if (response.isCorrect) {
          difficultyAccuracy[difficulty].correct++;
        }
      });
    });

    questionAnalysis.totalQuestionsAttempted = totalQuestions;
    questionAnalysis.averageTimePerQuestion = totalQuestions > 0 ? totalTime / totalQuestions : 0;

    // Calculate accuracy by difficulty
    Object.keys(difficultyAccuracy).forEach(difficulty => {
      const data = difficultyAccuracy[difficulty];
      questionAnalysis.accuracyByDifficulty[difficulty] = {
        accuracy: data.total > 0 ? (data.correct / data.total) * 100 : 0,
        questionsAttempted: data.total
      };
    });

    return questionAnalysis;
  }

  /**
   * Analyze performance by test type
   * @param {Array} allTests - Combined test data
   * @returns {object} - Test type performance
   */
  analyzeTestTypePerformance(allTests) {
    const testTypeStats = {};

    allTests.forEach(test => {
      const testType = test.testType || 'unknown';
      const score = test.score || test.totalScore || test.evaluationResult?.totalScore || 0;

      if (!testTypeStats[testType]) {
        testTypeStats[testType] = {
          count: 0,
          totalScore: 0,
          averageScore: 0,
          scores: []
        };
      }

      testTypeStats[testType].count++;
      testTypeStats[testType].totalScore += score;
      testTypeStats[testType].scores.push(score);
    });

    // Calculate averages
    Object.keys(testTypeStats).forEach(testType => {
      const stats = testTypeStats[testType];
      stats.averageScore = stats.count > 0 ? stats.totalScore / stats.count : 0;
    });

    return testTypeStats;
  }

  /**
   * Analyze time management patterns
   * @param {Array} allTests - Combined test data
   * @returns {object} - Time management insights
   */
  analyzeTimeManagement(allTests) {
    const timeInsights = {
      averageTestDuration: 0,
      timeEfficiencyTrend: 'stable',
      rushingIndicators: [],
      timeManagementScore: 0
    };

    const testsWithTime = allTests.filter(test => test.totalTimeTaken || test.duration);

    if (testsWithTime.length === 0) {
      return { message: 'No time data available' };
    }

    const durations = testsWithTime.map(test => test.totalTimeTaken || test.duration);
    timeInsights.averageTestDuration = durations.reduce((a, b) => a + b, 0) / durations.length;

    // Analyze time efficiency (score vs time relationship)
    const timeScorePairs = testsWithTime
      .filter(test => test.score || test.totalScore)
      .map(test => ({
        time: test.totalTimeTaken || test.duration,
        score: test.score || test.totalScore
      }));

    if (timeScorePairs.length >= 3) {
      // Simple correlation analysis
      const avgTime = timeScorePairs.reduce((sum, pair) => sum + pair.time, 0) / timeScorePairs.length;
      const avgScore = timeScorePairs.reduce((sum, pair) => sum + pair.score, 0) / timeScorePairs.length;

      timeInsights.timeManagementScore = avgScore / (avgTime / 60); // Score per minute
    }

    return timeInsights;
  }

  /**
   * Analyze difficulty progression over time
   * @param {Array} allTests - Combined test data
   * @returns {object} - Difficulty progression insights
   */
  analyzeDifficultyProgression(allTests) {
    const difficultyInsights = {
      difficultyTrend: 'stable',
      readinessForAdvanced: false,
      recommendedDifficultyLevel: 'medium'
    };

    // This would require difficulty data in tests
    // For now, return basic structure
    return difficultyInsights;
  }

  /**
   * Generate enhanced prompt for teachers with multi-student context
   * PRIVACY PROTECTION: Teachers do NOT have access to student chat conversations
   * @param {string} teacherId - Teacher ID
   * @param {string} classId - Class ID
   * @param {string} subject - Subject name
   * @param {string} message - Teacher message
   * @param {Array} conversationHistory - Previous conversation messages
   * @returns {Promise<string>} - Enhanced prompt with teacher context
   */
  async generateTeacherEnhancedPrompt(teacherId, classId, subject, message, conversationHistory = []) {
    if (!this.isConnected) {
      throw new Error('MCP service not connected');
    }

    try {
      console.log(`[MCP] Generating teacher enhanced prompt for teacher: ${teacherId}, class: ${classId}, subject: ${subject}`);

      // Fetch comprehensive class and student data
      // PRIVACY NOTE: Explicitly excluding chatConversations collection to protect student privacy
      const privacyContext = { isTeacherRequest: true };

      const [
        classStudents,
        studentsKnowledgeGraph,
        classTestHistory,
        classGradedResults
      ] = await Promise.all([
        this.getTeacherStudentsData(classId, subject),
        this.executeQuery('find', {
          collection: 'studentKnowledgeGraph',
          query: { 'curriculumProgress.subject': subject },
          projection: 'studentId curriculumProgress'
        }, privacyContext),
        this.executeQuery('find', {
          collection: 'testHistory',
          query: { subject },
          limit: 20,
          sort: { testDate: -1 },
          projection: 'subject testType topics testDate startTime duration numberOfQuestions totalMarks testInstructions createdBy'
        }, privacyContext),
        this.executeQuery('find', {
          collection: 'aegisGrader',
          query: { subject },
          limit: 15,
          sort: { gradedAt: -1 },
          projection: 'studentId testId overallScore questionAnalysis'
        }, privacyContext)
      ]);

      // Build teacher context with class analytics
      const teacherContext = this.buildTeacherContext(
        classStudents,
        studentsKnowledgeGraph.content,
        classTestHistory.content,
        classGradedResults.content,
        classId,
        subject
      );

      // Resolve curriculum nodes for better context
      const allCurriculumProgress = studentsKnowledgeGraph.content?.flatMap(kg => kg.curriculumProgress || []) || [];
      if (allCurriculumProgress.length > 0) {
        teacherContext.resolvedCurriculumNodes = await this.resolveCurriculumNodes(allCurriculumProgress, subject);
      }

      // Resolve question IDs from class test history
      const allQuestionIds = this.extractQuestionIdsFromTestHistory(
        classTestHistory.content || [],
        classGradedResults.content || []
      );

      if (allQuestionIds.length > 0) {
        // For teachers, try to determine class level from student data or use common defaults
        const studentClasses = classStudents.map(s => s.class || s.className).filter(Boolean);
        const mostCommonClass = this.getMostCommonClass(studentClasses);
        teacherContext.resolvedQuestions = await this.resolveQuestions(allQuestionIds, subject, mostCommonClass);
      }

      // Generate the enhanced teacher prompt
      const enhancedPrompt = `You are AegisAI for Teachers, an advanced AI teaching assistant with comprehensive access to class-wide student data. You provide insights, analytics, and recommendations to help teachers understand their students' performance and optimize their teaching strategies.

TEACHER CONTEXT:
${JSON.stringify(teacherContext, null, 2)}

CONVERSATION HISTORY:
${conversationHistory.map(msg => `${msg.type.toUpperCase()}: ${msg.content}`).join('\n')}

CURRENT TEACHER QUERY: ${message}

INSTRUCTIONS:
1. When asked about specific students' test performance, provide detailed analysis including:
   - Test subject, topics covered, and test type
   - Test date, duration, and number of questions
   - Performance metrics and scores
   - Areas where the student excelled or struggled
   - Specific recommendations for improvement
2. Use the detailedStudentData to provide comprehensive test insights
3. Reference specific test details from recentTestsOverview when discussing class performance
4. Analyze performance patterns across different test types and topics
5. Provide actionable insights for teaching strategies and interventions
6. Suggest differentiated instruction approaches based on individual test performance
7. Identify students who need additional support or acceleration
8. Keep responses professional and focused on educational outcomes
9. When discussing test results, be specific about dates, topics, and performance metrics
10. Focus on the subject: ${subject}

AVAILABLE DATA INCLUDES:
- Detailed test history for each student (test type, topics, dates, scores)
- Recent test attempts with performance analytics
- Class-wide test overview with comprehensive test details
- Individual student proficiency and performance trends
- Resolved curriculum nodes with meaningful topic names, descriptions, and hierarchical relationships
- Resolved questions with actual question text, options, answers, and explanations
- Curriculum progress mapped to actual topic content instead of raw nodeids

Respond as AegisAI for Teachers with data-driven insights and specific test analysis:`;

      console.log(`[MCP] Teacher enhanced prompt generated successfully (${enhancedPrompt.length} characters)`);
      return enhancedPrompt;

    } catch (error) {
      console.error('[MCP] Error generating teacher enhanced prompt:', error);
      throw error;
    }
  }

  /**
   * Get students data for a specific teacher's class and subject
   * @param {string} classId - Class ID
   * @param {string} subject - Subject name
   * @returns {Promise<Array>} - Array of student data
   */
  async getTeacherStudentsData(classId, subject) {
    try {
      console.log(`[MCP] Getting teacher students data for class: ${classId}, subject: ${subject}`);

      // Get class information with populated students
      // Convert classId to ObjectId if it's a string
      const query = { _id: mongoose.Types.ObjectId.isValid(classId) ? new mongoose.Types.ObjectId(classId) : classId };

      const classData = await this.executeQuery('find', {
        collection: 'classes',
        query: query,
        limit: 1
      }, { isTeacherRequest: true });

      if (!classData.content || classData.content.length === 0) {
        console.log(`[MCP] No class found with ID: ${classId}`);
        return [];
      }

      const classInfo = classData.content[0];
      const studentIds = classInfo.students || [];

      if (studentIds.length === 0) {
        console.log(`[MCP] No students found in class: ${classId}`);
        return [];
      }

      console.log(`[MCP] Found ${studentIds.length} students in class`);

      // Get detailed student data
      // Convert student IDs to ObjectIds if they're strings
      const studentObjectIds = studentIds.map(id =>
        mongoose.Types.ObjectId.isValid(id) ? new mongoose.Types.ObjectId(id) : id
      );

      const studentsData = await this.executeQuery('find', {
        collection: 'students',
        query: { _id: { $in: studentObjectIds } },
        projection: 'username email subjects proficiency'
      }, { isTeacherRequest: true });

      console.log(`[MCP] Retrieved data for ${studentsData.content.length} students`);

      // Filter and enhance student data for the specific subject with detailed test information
      const enhancedStudents = await Promise.all(studentsData.content.map(async (student) => {
        const subjectData = student.subjects?.find(s => s.subjectName === subject);

        // Get detailed test information for this student
        const detailedTestData = await this.getStudentDetailedTestHistory(student._id, subject, subjectData?.testHistory || [], student.class || student.className);

        return {
          _id: student._id,
          username: student.username,
          email: student.email,
          proficiency: student.proficiency || 0,
          subjectProficiency: subjectData?.overallProficiency || 0,
          testHistory: subjectData?.testHistory || [],
          detailedTestHistory: detailedTestData.testDetails || [],
          attemptedTests: subjectData?.attemptedTests || [],
          recentPerformance: this.calculateRecentPerformance(subjectData?.testHistory || []),
          testAnalytics: detailedTestData.analytics || {},
          resolvedQuestions: detailedTestData.resolvedQuestions || []
        };
      }));

      console.log(`[MCP] Enhanced student data for ${enhancedStudents.length} students`);
      return enhancedStudents;
    } catch (error) {
      console.error('[MCP] Error getting teacher students data:', error);
      return [];
    }
  }

  /**
   * Get detailed test history for a specific student with question resolution
   * @param {string} studentId - Student ID
   * @param {string} subject - Subject name
   * @param {Array} testHistoryIds - Array of test history IDs
   * @param {string} studentClass - Student's class level
   * @returns {Promise<object>} - Detailed test data, analytics, and resolved questions
   */
  async getStudentDetailedTestHistory(studentId, subject, testHistoryIds = [], studentClass = null) {
    try {
      if (testHistoryIds.length === 0) {
        return { testDetails: [], analytics: {}, resolvedQuestions: [] };
      }

      // Get detailed test information from TestHistory collection
      const testDetails = await this.executeQuery('find', {
        collection: 'testHistory',
        query: { _id: { $in: testHistoryIds } },
        sort: { testDate: -1 },
        limit: 10
      }, { isTeacherRequest: true });

      // Get graded results for this student
      const gradedResults = await this.executeQuery('find', {
        collection: 'aegisGrader',
        query: {
          'testDetails.subject': subject,
          'answerSheets.studentName': { $regex: studentId, $options: 'i' }
        },
        sort: { 'testDetails.date': -1 },
        limit: 5
      }, { isTeacherRequest: true });

      // Extract and resolve question IDs from this student's test history
      const questionIds = this.extractQuestionIdsFromTestHistory(
        testDetails.content || [],
        gradedResults.content || []
      );

      let resolvedQuestions = [];
      if (questionIds.length > 0) {
        resolvedQuestions = await this.resolveQuestions(questionIds, subject, studentClass);
      }

      // Combine and analyze test data
      const analytics = this.analyzeStudentTestPerformance(
        testDetails.content || [],
        gradedResults.content || []
      );

      return {
        testDetails: testDetails.content || [],
        gradedResults: gradedResults.content || [],
        resolvedQuestions,
        analytics
      };
    } catch (error) {
      console.error('[MCP] Error getting detailed test history:', error);
      return { testDetails: [], analytics: {}, resolvedQuestions: [] };
    }
  }

  /**
   * Analyze student test performance from detailed test data
   * @param {Array} testDetails - Detailed test information
   * @param {Array} gradedResults - Graded test results
   * @returns {object} - Performance analytics
   */
  analyzeStudentTestPerformance(testDetails, gradedResults) {
    const analytics = {
      totalTests: testDetails.length,
      recentTests: testDetails.slice(0, 3),
      performanceTrend: 'stable',
      strongAreas: [],
      weakAreas: [],
      averageScore: 0,
      testTypePerformance: {},
      difficultyAnalysis: {}
    };

    if (testDetails.length === 0) {
      return analytics;
    }

    // Analyze test types and topics
    const topicPerformance = {};
    const testTypeStats = {};

    testDetails.forEach(test => {
      // Track test type performance
      if (!testTypeStats[test.testType]) {
        testTypeStats[test.testType] = { count: 0, totalMarks: 0 };
      }
      testTypeStats[test.testType].count++;
      testTypeStats[test.testType].totalMarks += test.totalMarks || 0;

      // Track topic coverage
      if (test.topics && Array.isArray(test.topics)) {
        test.topics.forEach(topic => {
          if (!topicPerformance[topic]) {
            topicPerformance[topic] = { tests: 0, coverage: 'basic' };
          }
          topicPerformance[topic].tests++;
        });
      }
    });

    analytics.testTypePerformance = testTypeStats;
    analytics.topicCoverage = topicPerformance;

    return analytics;
  }

  /**
   * Build teacher context with class analytics
   * @param {Array} classStudents - Students in the class
   * @param {Array} knowledgeGraphData - Knowledge graph data for students
   * @param {Array} testHistory - Test history data
   * @param {Array} gradedResults - Graded test results
   * @param {string} classId - Class ID
   * @param {string} subject - Subject name
   * @returns {object} - Teacher context object
   */
  buildTeacherContext(classStudents, knowledgeGraphData, testHistory, gradedResults, classId, subject) {
    const context = {
      classInfo: {
        classId,
        subject,
        totalStudents: classStudents.length,
        averageClassProficiency: this.calculateClassAverageProficiency(classStudents)
      },
      studentPerformance: {
        highPerformers: classStudents.filter(s => s.subjectProficiency > 0.8),
        averagePerformers: classStudents.filter(s => s.subjectProficiency >= 0.5 && s.subjectProficiency <= 0.8),
        strugglingStudents: classStudents.filter(s => s.subjectProficiency < 0.5)
      },
      detailedStudentData: classStudents.map(student => ({
        username: student.username,
        proficiency: student.subjectProficiency,
        recentTests: student.detailedTestHistory?.slice(0, 3) || [],
        testAnalytics: student.testAnalytics || {},
        attemptedTests: student.attemptedTests?.slice(-3) || []
      })),
      recentTestsOverview: testHistory.slice(0, 5).map(test => ({
        testType: test.testType,
        topics: test.topics,
        testDate: test.testDate,
        duration: test.duration,
        numberOfQuestions: test.numberOfQuestions,
        totalMarks: test.totalMarks
      })),
      classAnalytics: {
        recentTestPerformance: this.analyzeClassTestPerformance(testHistory, classStudents),
        knowledgeGaps: this.identifyClassKnowledgeGaps(knowledgeGraphData, classStudents),
        performanceTrends: this.analyzeClassPerformanceTrends(testHistory, classStudents)
      },
      recommendations: {
        interventionNeeded: classStudents.filter(s => s.subjectProficiency < 0.4).map(s => s.username),
        accelerationCandidates: classStudents.filter(s => s.subjectProficiency > 0.9).map(s => s.username),
        focusAreas: this.identifyClassFocusAreas(knowledgeGraphData, testHistory)
      }
    };

    return context;
  }

  /**
   * Calculate class average proficiency
   * @param {Array} students - Array of student objects
   * @returns {number} - Average proficiency
   */
  calculateClassAverageProficiency(students) {
    if (students.length === 0) return 0;
    const total = students.reduce((sum, student) => sum + (student.subjectProficiency || 0), 0);
    return Math.round((total / students.length) * 100) / 100;
  }

  /**
   * Analyze class test performance
   * @param {Array} testHistory - Test history data
   * @param {Array} students - Student data
   * @returns {object} - Class test performance analysis
   */
  analyzeClassTestPerformance(testHistory, students) {
    const studentIds = students.map(s => s._id.toString());
    const classTests = testHistory.filter(test => studentIds.includes(test.studentId?.toString()));

    if (classTests.length === 0) {
      return { message: 'No test data available for this class' };
    }

    const totalScore = classTests.reduce((sum, test) => sum + (test.score || 0), 0);
    const averageScore = totalScore / classTests.length;

    return {
      totalTests: classTests.length,
      averageScore: Math.round(averageScore * 100) / 100,
      participationRate: (classTests.length / students.length) * 100,
      recentTests: classTests.slice(-5)
    };
  }

  /**
   * Identify class knowledge gaps
   * @param {Array} knowledgeGraphData - Knowledge graph data
   * @param {Array} students - Student data
   * @returns {Array} - Common knowledge gaps
   */
  identifyClassKnowledgeGaps(knowledgeGraphData, students) {
    // Simplified implementation - would need more complex analysis in production
    return [
      { topic: 'Algebra Fundamentals', strugglingCount: 3 },
      { topic: 'Geometry Basics', strugglingCount: 2 }
    ];
  }

  /**
   * Analyze class performance trends
   * @param {Array} testHistory - Test history data
   * @param {Array} students - Student data
   * @returns {object} - Performance trends
   */
  analyzeClassPerformanceTrends(testHistory, students) {
    return {
      trend: 'improving',
      details: 'Class average has improved by 5% over the last month'
    };
  }

  /**
   * Identify class focus areas
   * @param {Array} knowledgeGraphData - Knowledge graph data
   * @param {Array} testHistory - Test history data
   * @returns {Array} - Focus areas for the class
   */
  identifyClassFocusAreas(knowledgeGraphData, testHistory) {
    return [
      'Problem-solving strategies',
      'Conceptual understanding',
      'Application of formulas'
    ];
  }

  /**
   * Calculate recent performance for a student
   * @param {Array} testHistory - Student's test history
   * @returns {object} - Recent performance metrics
   */
  calculateRecentPerformance(testHistory) {
    if (!testHistory || testHistory.length === 0) {
      return { averageScore: 0, trend: 'no_data' };
    }

    const recentTests = testHistory.slice(-3);
    const averageScore = recentTests.reduce((sum, test) => sum + (test.score || 0), 0) / recentTests.length;

    return {
      averageScore: Math.round(averageScore * 100) / 100,
      trend: 'stable',
      testCount: recentTests.length
    };
  }

  /**
   * Generate class analytics for teachers
   * @param {string} classId - Class ID
   * @param {string} subject - Subject name
   * @param {string} analyticsType - Type of analytics requested
   * @returns {Promise<object>} - Class analytics
   */
  async generateClassAnalytics(classId, subject, analyticsType = 'overview') {
    try {
      const studentsData = await this.getTeacherStudentsData(classId, subject);

      const analytics = {
        classId,
        subject,
        analyticsType,
        generatedAt: new Date().toISOString(),
        studentCount: studentsData.length,
        overview: {
          averageProficiency: this.calculateClassAverageProficiency(studentsData),
          performanceDistribution: {
            high: studentsData.filter(s => s.subjectProficiency > 0.8).length,
            medium: studentsData.filter(s => s.subjectProficiency >= 0.5 && s.subjectProficiency <= 0.8).length,
            low: studentsData.filter(s => s.subjectProficiency < 0.5).length
          }
        }
      };

      return analytics;
    } catch (error) {
      console.error('[MCP] Error generating class analytics:', error);
      throw error;
    }
  }

  /**
   * Resolve curriculum nodeids to meaningful content
   * @param {Array} curriculumProgress - Array of curriculum progress items with nodeIds
   * @param {string} subject - Subject name for dynamic model selection
   * @returns {Promise<Array>} - Array of resolved curriculum nodes with meaningful content
   */
  async resolveCurriculumNodes(curriculumProgress, subject) {
    try {
      if (!curriculumProgress || curriculumProgress.length === 0) {
        return [];
      }

      console.log(`[MCP] Resolving ${curriculumProgress.length} curriculum nodes for subject: ${subject}`);

      // Extract nodeIds from curriculum progress
      const nodeIds = curriculumProgress.map(progress => progress.nodeId).filter(Boolean);

      if (nodeIds.length === 0) {
        return [];
      }

      // Get curriculum nodes using the dynamic model
      const curriculumNodes = await this.executeQuery('find', {
        collection: 'curriculumNodes',
        query: { _id: { $in: nodeIds } },
        subject: subject
      });

      // Create a map for quick lookup
      const nodeMap = new Map();
      (curriculumNodes.content || []).forEach(node => {
        nodeMap.set(node._id.toString(), node);
      });

      // Resolve each curriculum progress item with meaningful content
      const resolvedNodes = curriculumProgress.map(progress => {
        const node = nodeMap.get(progress.nodeId.toString());
        return {
          nodeId: progress.nodeId,
          nodeModel: progress.nodeModel,
          status: progress.status,
          proficiency: progress.proficiency,
          subject: progress.subject,
          // Resolved meaningful content
          resolvedContent: node ? {
            name: node.name,
            description: node.description,
            type: node.type, // 'Chapter' or 'Subtopic'
            class: node.class,
            order: node.order,
            parents: node.parents || [],
            children: node.children || [],
            hierarchicalPath: this.buildHierarchicalPath(node, nodeMap)
          } : {
            error: 'Node not found',
            nodeId: progress.nodeId
          }
        };
      });

      console.log(`[MCP] Successfully resolved ${resolvedNodes.length} curriculum nodes`);
      return resolvedNodes;
    } catch (error) {
      console.error('[MCP] Error resolving curriculum nodes:', error);
      return [];
    }
  }

  /**
   * Build hierarchical path for a curriculum node
   * @param {object} node - The curriculum node
   * @param {Map} nodeMap - Map of all nodes for lookup
   * @returns {string} - Hierarchical path (e.g., "Chapter 1: Algebra > Subtopic: Linear Equations")
   */
  buildHierarchicalPath(node, nodeMap) {
    try {
      if (node.type === 'Chapter') {
        return `Chapter: ${node.name}`;
      }

      if (node.type === 'Subtopic' && node.parents && node.parents.length > 0) {
        const parentNode = nodeMap.get(node.parents[0].toString());
        if (parentNode) {
          return `Chapter: ${parentNode.name} > Subtopic: ${node.name}`;
        }
      }

      return `${node.type}: ${node.name}`;
    } catch (error) {
      console.error('[MCP] Error building hierarchical path:', error);
      return `${node.type}: ${node.name}`;
    }
  }

  /**
   * Resolve question IDs to meaningful content with proper collection prioritization
   * @param {Array} questionIds - Array of question IDs to resolve
   * @param {string} subject - Subject name for context
   * @param {string} studentClass - Student's class level (e.g., "Class 10", "Class 12")
   * @param {string} specificCollection - Optional specific question collection name
   * @returns {Promise<Array>} - Array of resolved questions with meaningful content
   */
  async resolveQuestions(questionIds, subject, studentClass = null, specificCollection = null) {
    try {
      if (!questionIds || questionIds.length === 0) {
        return [];
      }

      console.log(`[MCP] Resolving ${questionIds.length} questions for subject: ${subject}, class: ${studentClass}`);

      const resolvedQuestions = [];
      let remainingQuestionIds = [...questionIds];

      // Generate collection names to try in priority order
      const collectionsToTry = this.generateQuestionCollectionNames(subject, studentClass, specificCollection);

      for (const collectionConfig of collectionsToTry) {
        if (remainingQuestionIds.length === 0) break;

        try {
          console.log(`[MCP] Trying collection: ${collectionConfig.collectionName || collectionConfig.collection}`);

          const questions = await this.executeQuery('find', {
            collection: collectionConfig.collection,
            query: { _id: { $in: remainingQuestionIds } },
            questionCollection: collectionConfig.collectionName
          });

          if (questions.content && questions.content.length > 0) {
            console.log(`[MCP] Found ${questions.content.length} questions in ${collectionConfig.collectionName || collectionConfig.collection}`);

            const resolvedFromThisCollection = questions.content.map(question => ({
              questionId: question._id,
              source: collectionConfig.collectionName || collectionConfig.collection,
              priority: collectionConfig.priority,
              resolvedContent: {
                question: question.question,
                options: question.options || [],
                answer: question.answer,
                solution: question.solution,
                topic: question.topic || question.topics,
                subtopic: question.subtopic || question.subtopics,
                difficulty: question.difficulty,
                bloomTaxonomy: question.bloomTaxonomy,
                passage: question.passage,
                images: {
                  questionImages: question.queimage || question.images || [],
                  answerImages: question.ansimage || []
                },
                metadata: {
                  differentiatingParameter: question.differentiatingParameter,
                  metaDataCollection: question.metaDataCollection,
                  discriminationParameter: question.discrimination_parameter,
                  collectionSource: collectionConfig.collectionName || collectionConfig.collection
                }
              }
            }));

            resolvedQuestions.push(...resolvedFromThisCollection);

            // Remove found question IDs from the remaining list
            const foundIds = questions.content.map(q => q._id.toString());
            remainingQuestionIds = remainingQuestionIds.filter(id => !foundIds.includes(id.toString()));
          }
        } catch (collectionError) {
          console.warn(`[MCP] Could not search in collection ${collectionConfig.collectionName || collectionConfig.collection}:`, collectionError.message);
        }
      }

      // Add entries for questions that couldn't be resolved
      remainingQuestionIds.forEach(unresolvedId => {
        resolvedQuestions.push({
          questionId: unresolvedId,
          source: 'not_found',
          priority: 999,
          resolvedContent: {
            error: 'Question not found in any collection',
            questionId: unresolvedId,
            searchedCollections: collectionsToTry.map(c => c.collectionName || c.collection)
          }
        });
      });

      console.log(`[MCP] Successfully resolved ${resolvedQuestions.length} questions from ${collectionsToTry.length} collections`);
      return resolvedQuestions;
    } catch (error) {
      console.error('[MCP] Error resolving questions:', error);
      return [];
    }
  }

  /**
   * Generate question collection names in priority order
   * @param {string} subject - Subject name
   * @param {string} studentClass - Student's class level
   * @param {string} specificCollection - Optional specific collection name
   * @returns {Array} - Array of collection configurations in priority order
   */
  generateQuestionCollectionNames(subject, studentClass = null, specificCollection = null) {
    const collections = [];
    const subjectLower = subject.toLowerCase();

    // Priority 1: Specific collection if provided
    if (specificCollection) {
      collections.push({
        collection: 'questions',
        collectionName: specificCollection,
        priority: 1
      });
    }

    // Priority 2: UPSC-specific collections (handle competitive exams)
    if (this.isCompetitiveExam(subject, studentClass)) {
      const examSubject = this.getExamSubjectForCollection(subject, studentClass);

      // UPSC follows the pattern: questionBank_class13_upsc-prelims
      collections.push(
        { collection: 'questions', collectionName: `questionBank_class13_${examSubject}`, priority: 2 },
        { collection: 'questions', collectionName: `questionBank_class12_${examSubject}`, priority: 3 },
        { collection: 'questions', collectionName: `questionBank_class14_${examSubject}`, priority: 4 }
      );

      // Also try subject-specific collections for UPSC
      if (examSubject === 'upsc-prelims') {
        const subjectSpecific = this.getExamSubject(subject);
        collections.push(
          { collection: 'questions', collectionName: `questionBank_class13_${subjectSpecific}`, priority: 5 },
          { collection: 'questions', collectionName: `questionBank_class12_${subjectSpecific}`, priority: 6 }
        );
      }

      // Additional UPSC collection patterns
      collections.push(
        { collection: 'questions', collectionName: `questionBank_upsc_${examSubject}`, priority: 7 },
        { collection: 'questions', collectionName: `questionBank_${examSubject}`, priority: 8 },
        { collection: 'questions', collectionName: `upsc_${examSubject}`, priority: 9 },
        { collection: 'questions', collectionName: `${examSubject}_questions`, priority: 10 }
      );
    }

    // Priority 3: Subject and class specific collections (for school students)
    if (studentClass && !this.isCompetitiveExam(subject, studentClass)) {
      const classNumber = this.extractClassNumber(studentClass);
      if (classNumber) {
        // Try exact class and subject match
        collections.push({
          collection: 'questions',
          collectionName: `questionBank_class${classNumber}_${subjectLower}`,
          priority: 10
        });

        // Try common subject variations
        const subjectVariations = this.getSubjectVariations(subjectLower);
        subjectVariations.forEach((variation, index) => {
          collections.push({
            collection: 'questions',
            collectionName: `questionBank_class${classNumber}_${variation}`,
            priority: 11 + index
          });
        });
      }
    }

    // Priority 3: Default class collections (Class 10 and 12 are most common)
    const defaultClasses = ['10', '12', '11', '9'];
    defaultClasses.forEach((classNum, index) => {
      collections.push({
        collection: 'questions',
        collectionName: `questionBank_class${classNum}_${subjectLower}`,
        priority: 10 + index
      });

      // Try subject variations for default classes
      const subjectVariations = this.getSubjectVariations(subjectLower);
      subjectVariations.forEach((variation, varIndex) => {
        collections.push({
          collection: 'questions',
          collectionName: `questionBank_class${classNum}_${variation}`,
          priority: 20 + (index * 10) + varIndex
        });
      });
    });

    // Remove duplicates while preserving priority order
    const uniqueCollections = [];
    const seenCollections = new Set();

    collections.forEach(config => {
      const key = `${config.collection}_${config.collectionName}`;
      if (!seenCollections.has(key)) {
        seenCollections.add(key);
        uniqueCollections.push(config);
      }
    });

    // Sort by priority (lower number = higher priority)
    uniqueCollections.sort((a, b) => a.priority - b.priority);

    console.log(`[MCP] Generated ${uniqueCollections.length} question collections to try for ${subject}, class: ${studentClass}`);
    return uniqueCollections;
  }

  /**
   * Extract class number from class string
   * @param {string} studentClass - Class string like "Class 10", "10", "Grade 10"
   * @returns {string|null} - Extracted class number or null
   */
  extractClassNumber(studentClass) {
    if (!studentClass) return null;

    // Extract number from various formats
    const match = studentClass.match(/(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Get subject variations for better collection matching
   * @param {string} subject - Subject name in lowercase
   * @returns {Array} - Array of subject variations
   */
  getSubjectVariations(subject) {
    const variations = [];

    // Common subject mappings
    const subjectMappings = {
      'mathematics': ['math', 'maths'],
      'math': ['mathematics', 'maths'],
      'maths': ['mathematics', 'math'],
      'physics': ['phy'],
      'chemistry': ['chem'],
      'biology': ['bio'],
      'computer': ['cs', 'computerscience', 'computer_science'],
      'computerscience': ['computer', 'cs', 'computer_science'],
      'computer_science': ['computer', 'cs', 'computerscience'],
      'english': ['eng'],
      'hindi': ['hin'],
      'social': ['socialscience', 'social_science', 'sst'],
      'socialscience': ['social', 'social_science', 'sst'],
      'social_science': ['social', 'socialscience', 'sst']
    };

    if (subjectMappings[subject]) {
      variations.push(...subjectMappings[subject]);
    }

    return variations;
  }

  /**
   * Extract question IDs from test history data
   * @param {Array} detailedTestHistory - Detailed test history from TestHistory collection
   * @param {Array} gradedResults - Graded test results from AegisGrader collection
   * @returns {Array} - Array of unique question IDs
   */
  extractQuestionIdsFromTestHistory(detailedTestHistory, gradedResults) {
    try {
      const questionIds = new Set();

      // Extract from detailed test history
      detailedTestHistory.forEach(test => {
        // Check for questions array
        if (test.questions && Array.isArray(test.questions)) {
          test.questions.forEach(questionId => {
            if (questionId) {
              questionIds.add(questionId.toString());
            }
          });
        }

        // Check for responses with question references
        if (test.responses && Array.isArray(test.responses)) {
          test.responses.forEach(response => {
            if (response.questionId) {
              questionIds.add(response.questionId.toString());
            }
            // Also check for question_id (alternative naming)
            if (response.question_id) {
              questionIds.add(response.question_id.toString());
            }
            // Check for nested question object
            if (response.question && response.question._id) {
              questionIds.add(response.question._id.toString());
            }
          });
        }

        // Check for questionBank references
        if (test.questionBank && Array.isArray(test.questionBank)) {
          test.questionBank.forEach(questionId => {
            if (questionId) {
              questionIds.add(questionId.toString());
            }
          });
        }

        // Check for questionIds array (alternative naming)
        if (test.questionIds && Array.isArray(test.questionIds)) {
          test.questionIds.forEach(questionId => {
            if (questionId) {
              questionIds.add(questionId.toString());
            }
          });
        }

        // Check for test structure with embedded questions
        if (test.testQuestions && Array.isArray(test.testQuestions)) {
          test.testQuestions.forEach(q => {
            if (q._id) {
              questionIds.add(q._id.toString());
            }
            if (q.questionId) {
              questionIds.add(q.questionId.toString());
            }
          });
        }
      });

      // Extract from graded results
      gradedResults.forEach(result => {
        // Check answer sheets for question references
        if (result.answerSheets && Array.isArray(result.answerSheets)) {
          result.answerSheets.forEach(sheet => {
            if (sheet.responses && Array.isArray(sheet.responses)) {
              sheet.responses.forEach(response => {
                if (response.questionId) {
                  questionIds.add(response.questionId.toString());
                }
                if (response.question_id) {
                  questionIds.add(response.question_id.toString());
                }
                // Check for nested question object
                if (response.question && response.question._id) {
                  questionIds.add(response.question._id.toString());
                }
              });
            }

            // Check for questions array in answer sheet
            if (sheet.questions && Array.isArray(sheet.questions)) {
              sheet.questions.forEach(questionId => {
                if (questionId) {
                  questionIds.add(questionId.toString());
                }
              });
            }
          });
        }

        // Check test details for question references
        if (result.testDetails && result.testDetails.questions) {
          if (Array.isArray(result.testDetails.questions)) {
            result.testDetails.questions.forEach(questionId => {
              if (questionId) {
                questionIds.add(questionId.toString());
              }
            });
          }
        }

        // Check for questionBank in test details
        if (result.testDetails && result.testDetails.questionBank && Array.isArray(result.testDetails.questionBank)) {
          result.testDetails.questionBank.forEach(questionId => {
            if (questionId) {
              questionIds.add(questionId.toString());
            }
          });
        }

        // Check for direct questions array in result
        if (result.questions && Array.isArray(result.questions)) {
          result.questions.forEach(questionId => {
            if (questionId) {
              questionIds.add(questionId.toString());
            }
          });
        }
      });

      const uniqueQuestionIds = Array.from(questionIds);
      console.log(`[MCP] Extracted ${uniqueQuestionIds.length} unique question IDs from test history`);
      return uniqueQuestionIds;
    } catch (error) {
      console.error('[MCP] Error extracting question IDs from test history:', error);
      return [];
    }
  }

  /**
   * Get the most common class from an array of class values
   * @param {Array} classes - Array of class values
   * @returns {string|null} - Most common class or null
   */
  getMostCommonClass(classes) {
    if (!classes || classes.length === 0) return null;

    const classCount = {};
    classes.forEach(cls => {
      if (cls) {
        classCount[cls] = (classCount[cls] || 0) + 1;
      }
    });

    let mostCommon = null;
    let maxCount = 0;

    Object.entries(classCount).forEach(([cls, count]) => {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = cls;
      }
    });

    return mostCommon;
  }

  /**
   * Check if the subject/class indicates a competitive exam
   * @param {string} subject - Subject name
   * @param {string} studentClass - Student's class level
   * @returns {boolean} - True if it's a competitive exam
   */
  isCompetitiveExam(subject, studentClass) {
    const competitiveKeywords = ['upsc', 'prelims', 'mains', 'ias', 'ips', 'jee', 'neet', 'cat', 'gate'];
    const subjectLower = (subject || '').toLowerCase();
    const classLower = (studentClass || '').toLowerCase();

    return competitiveKeywords.some(keyword =>
      subjectLower.includes(keyword) || classLower.includes(keyword)
    );
  }

  /**
   * Get exam type from subject/class context
   * @param {string} subject - Subject name
   * @param {string} studentClass - Student's class level
   * @returns {string} - Exam type (e.g., 'upsc', 'jee')
   */
  getExamType(subject, studentClass) {
    const text = `${subject || ''} ${studentClass || ''}`.toLowerCase();

    if (text.includes('upsc') || text.includes('prelims') || text.includes('mains') || text.includes('ias')) {
      return 'upsc';
    }
    if (text.includes('jee')) return 'jee';
    if (text.includes('neet')) return 'neet';
    if (text.includes('cat')) return 'cat';
    if (text.includes('gate')) return 'gate';

    return 'upsc'; // Default to UPSC for competitive exams
  }

  /**
   * Get exam subject from subject name
   * @param {string} subject - Subject name
   * @returns {string} - Standardized exam subject
   */
  getExamSubject(subject) {
    const subjectLower = (subject || '').toLowerCase();

    // UPSC subject mappings
    const upscSubjects = {
      'economy': 'economy',
      'economics': 'economy',
      'economic': 'economy',
      'polity': 'polity',
      'politics': 'polity',
      'political': 'polity',
      'history': 'history',
      'geography': 'geography',
      'environment': 'environment',
      'science': 'science',
      'technology': 'science',
      'current': 'currentaffairs',
      'affairs': 'currentaffairs',
      'general': 'generalstudies',
      'studies': 'generalstudies'
    };

    for (const [key, value] of Object.entries(upscSubjects)) {
      if (subjectLower.includes(key)) {
        return value;
      }
    }

    return subjectLower.replace(/\s+/g, ''); // Remove spaces as fallback
  }

  /**
   * Get exam subject formatted for collection names (handles UPSC-specific format)
   * @param {string} subject - Subject name
   * @param {string} studentClass - Student's class level
   * @returns {string} - Collection-formatted exam subject
   */
  getExamSubjectForCollection(subject, studentClass) {
    const classLower = (studentClass || '').toLowerCase();

    // Check if it's UPSC Prelims specifically
    if (classLower.includes('prelims') || classLower.includes('upsc')) {
      // For UPSC Prelims, use the format: upsc-prelims
      if (classLower.includes('prelims')) {
        return 'upsc-prelims';
      }
      // For UPSC Mains, use: upsc-mains
      if (classLower.includes('mains')) {
        return 'upsc-mains';
      }
      // Default UPSC format
      return 'upsc-prelims';
    }

    // For other competitive exams, use the exam type
    const examType = this.getExamType(subject, studentClass);
    if (examType !== 'upsc') {
      return examType;
    }

    // Fallback to subject-based mapping
    return this.getExamSubject(subject);
  }
}

// Create singleton instance
const mongoMcpService = new MongoMcpService();

export default mongoMcpService;
