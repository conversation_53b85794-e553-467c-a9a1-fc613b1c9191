import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import { createKnowledgeGraphModel } from '../models/knowledgeGraphModel.js';

class StudentCurriculumService {
  /**
   * Initialize curriculum progress for a student
   * @param {string} studentId - Unique identifier for the student
   * @param {string} className - Class name (e.g., "Class 10")
   * @param {string} subject - Subject name (e.g., "Physics")
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session
   */
  async initializeStudentCurriculum(studentId, className, subject, session = null) {
    try {
      // Check if curriculum exists for this student
      let studentCurriculum = await StudentCurriculumModel.findOne({ studentId, 'curriculumProgress.subject': subject }).session(session || undefined);

      if (studentCurriculum) {
        return studentCurriculum;
      }

      // Create the subject-specific model and ensure it's registered
      const CurriculumNode = createKnowledgeGraphModel(subject);
      console.error(`curriculum node model-> ${CurriculumNode.modelName}`);

      // Get all curriculum nodes (chapters and subtopics)
      console.error("ClassName:", className); // Check the value
      const modelName = CurriculumNode.modelName; // Get the actual model name
      const curriculumNodes = await CurriculumNode.find({
        class: { $regex: new RegExp(className, "i") }, // Case-insensitive search
      })
        .populate({
          path: 'children',
          model: modelName, // Explicitly specify the model name
          populate: {
            path: 'children', // This gets grandchildren too if needed
            model: modelName // Explicitly specify the model name for nested populate
          }
        })
        .sort({ order: 1 })
        .session(session || undefined);
      console.error("Query:", { class: className });
      console.error("Found nodes:", curriculumNodes);

      // Collect all nodes including chapters and subtopics
      console.log("curriculumNodes:", curriculumNodes);
      const allNodes = [];

      // Add chapters
      curriculumNodes.forEach(chapter => {
        allNodes.push(chapter);

        // Add subtopics
        if (chapter.children && chapter.children.length > 0) {
          chapter.children.forEach(subtopic => {
            allNodes.push(subtopic);
          });
        }
      });

      // Handle case where no curriculum nodes exist for this subject
      if (allNodes.length === 0) {
        console.warn(`No curriculum nodes found for subject ${subject} and class ${className}. Creating empty curriculum.`);

        // Create an empty curriculum that can be populated later
        studentCurriculum = new StudentCurriculumModel({
          studentId,
          subject,
          curriculumProgress: [], // Empty progress array
          totalProgress: 0,
          className: className
        });
      } else {
        // Create progress records for all nodes
        studentCurriculum = new StudentCurriculumModel({
          studentId,
          subject,
          curriculumProgress: allNodes.map(node => ({
            nodeId: node._id,
            nodeModel: `CurriculumNode_${subject}`, // Explicitly set the model name
            status: 'Not Started',
            subject: subject,
            progress: 0,
          })),
          totalProgress: 0,
          className: className
        });
      }

      await studentCurriculum.save({ session: session || undefined });
      return studentCurriculum;
    } catch (error) {
      console.error(`Error initializing curriculum for student ${studentId}:`, error);
      throw error; // Re-throw the error instead of returning undefined
    }
  }

  /**
   * Update progress for a specific curriculum node
   * @param {string} studentId - Unique identifier for the student
   * @param {string} nodeId - ID of the curriculum node (chapter/subtopic)
   */
  async updateNodeProgress(studentId, nodeId) {
    try {
      const studentCurriculum = await StudentCurriculumModel.findOne({ studentId });

      if (!studentCurriculum) {
        console.error('Student curriculum not found');
        return null;
      }

      // Find the specific node progress
      const nodeProgress = studentCurriculum.curriculumProgress.find(
        progress => progress.nodeId.toString() === nodeId
      );

      if (!nodeProgress) {
        console.error('Node not found in student curriculum');
        return null;
      }

      // Update node progress
      nodeProgress.status = 'Completed';
      nodeProgress.progress = 100;

      // Recalculate overall curriculum progress
      const completedNodes = studentCurriculum.curriculumProgress.filter(
        progress => progress.status === 'Completed'
      );
      studentCurriculum.totalProgress =
        (completedNodes.length / studentCurriculum.curriculumProgress.length) * 100;

      await studentCurriculum.save();
      return studentCurriculum;
    } catch (error) {
      console.error(`Error updating node progress for student ${studentId}:`, error);
      throw error;
    }
  }

  /**
   * Retrieve a student's curriculum progress
   * @param {string} studentId - Unique identifier for the student
   */
  async getStudentProgress(studentId) {
    try {
      // First get the student curriculum to determine the subject and model name
      const studentCurriculum = await StudentCurriculumModel.findOne({ studentId });

      if (!studentCurriculum) {
        console.error('Student curriculum not found');
        return null;
      }

      // Get the subject to determine the correct model name
      const subject = studentCurriculum.subject;
      const modelName = `CurriculumNode_${subject}`;

      // Now populate with the correct model name
      const populatedCurriculum = await StudentCurriculumModel.findOne({ studentId })
        .populate({
          path: 'curriculumProgress.nodeId',
          model: modelName,
          select: 'name type order parents children',
          populate: [
            { path: 'parents', model: modelName, select: 'name type' },
            { path: 'children', model: modelName, select: 'name type' }
          ]
        });

      if (!populatedCurriculum) {
        console.error('Student curriculum not found');
        return null;
      }

      return {
        totalProgress: populatedCurriculum.totalProgress,
        className: populatedCurriculum.className || "Class 10",
        curriculumProgress: populatedCurriculum.curriculumProgress.map(progress => ({
          node: progress.nodeId.name,
          type: progress.nodeId.type,
          order: progress.nodeId.order,
          status: progress.status,
          progress: progress.progress,
          relationships: {
            parents: progress.nodeId.parents?.map(parent => ({
              id: parent._id,
              name: parent.name,
              type: parent.type
            })) || [],
            children: progress.nodeId.children?.map(child => ({
              id: child._id,
              name: child.name,
              type: child.type
            })) || []
          }
        })),
      };
    } catch (error) {
      console.error(`Error retrieving progress for student ${studentId}:`, error);
      throw error;
    }
  }

  /**
   * Get the curriculum graph for a specific class
   * @param {string} className - Class name (e.g., "Class 10")
   * @param {string} subject - Subject name
   */
  async getCurriculumGraph(className = "Class 10", subject = "Physics") {
    try {
      const CurriculumNode = createKnowledgeGraphModel(subject);
      const visualizedCurriculum = await CurriculumNode.visualizeCurriculum();
      return visualizedCurriculum;
    } catch (error) {
      console.error(`Error retrieving curriculum graph for ${className}:`, error);
      throw error;
    }
  }

  /**
   * Delete a student's curriculum by knowledgeGraphId
   * @param {string} knowledgeGraphId
   * @param {mongoose.ClientSession} [session] - Optional MongoDB session
   */
  async deleteStudentCurriculum(knowledgeGraphId, session = null) {
    try {
      const result = await StudentCurriculumModel.deleteOne({ _id: knowledgeGraphId }).session(session || undefined);
      if (result.deletedCount === 0) {
        console.error(`No curriculum found for knowledgeGraphId: ${knowledgeGraphId}`);
        return null;
      }
      console.log(`Curriculum deleted for knowledgeGraphId: ${knowledgeGraphId}`);
      return result;
    } catch (error) {
      console.error(`Error deleting curriculum for knowledgeGraphId ${knowledgeGraphId}:`, error);
      throw error;
    }
  }
}

export default new StudentCurriculumService();
